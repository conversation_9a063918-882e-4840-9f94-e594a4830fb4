# AI Integration System Environment Configuration
# إعدادات نظام التكامل الذكي

# ===== Gemini CLI Configuration =====
GEMINI_API_KEY=AIzaSyAdt3Y3CIkv9zIb0__rRoNazF6bepTm4UY
GEMINI_CLI_PATH=C:/Users/<USER>/gemini-cli
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=2048

# Alternative API Keys (for rotation)
GEMINI_API_KEY_2=AIzaSyC0rKnC_9RA0bBo4Je4MtQy4tMggTKqsN0
GEMINI_API_KEY_3=AIzaSyCbwKjebsrFkpK3tWxa6OgjNwOWcr5mpF4
GEMINI_API_KEY_4=AIzaSyA-X9PRGYWZjRsiU98PqVtLt6v1XacZjhg

# ===== Ollama Configuration =====
OLLAMA_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=llama3:8b
OLLAMA_MEMORY_MODEL=llama3:8b
OLLAMA_FILE_SEARCH_MODEL=llama3:8b
OLLAMA_TERMINAL_MODEL=codellama:7b
OLLAMA_DATA_ANALYSIS_MODEL=mistral:7b

# ===== AnythingLLM Configuration =====
ANYTHINGLLM_URL=http://localhost:4001
ANYTHINGLLM_WORKSPACE=model-mix
ANYTHINGLLM_API_KEY=
ANYTHINGLLM_EMBEDDING_MODEL=all-MiniLM-L6-v2

# ===== n8n Configuration =====
N8N_URL=http://localhost:5678
N8N_USERNAME=admin
N8N_PASSWORD=password123
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# ===== Port Configuration =====
# Current Ports
PORT_ANYTHINGLLM=4001
PORT_N8N=5678
PORT_OLLAMA=11434
PORT_FRONTEND=3000

# Proposed Unified Ports
PORT_UNIFIED_ANYTHINGLLM=3001
PORT_UNIFIED_N8N=3002
PORT_UNIFIED_OLLAMA=3003
PORT_UNIFIED_FRONTEND=3000
PORT_UNIFIED_API_GATEWAY=3004

# ===== AI Agents Configuration =====
AGENTS_BASE_PATH=./ai-agents
AGENTS_COORDINATOR=agent-coordinator.py
AGENTS_LOG_LEVEL=INFO
AGENTS_MAX_CONCURRENT=3
AGENTS_TIMEOUT=60

# Agent-specific settings
MEMORY_AGENT_MODEL=llama3:8b
FILE_SEARCH_AGENT_MODEL=llama3:8b
TERMINAL_AGENT_MODEL=codellama:7b
DATA_ANALYSIS_AGENT_MODEL=mistral:7b

# ===== Memory Management =====
MEMORY_BASE_PATH=./memory
MEMORY_SESSIONS_PATH=./memory/sessions
MEMORY_PROJECTS_PATH=./memory/projects
MEMORY_AGENTS_PATH=./memory/agents
MEMORY_INTEGRATION_PATH=./memory/integration
MEMORY_MAX_SIZE_MB=1024
MEMORY_CLEANUP_DAYS=30

# ===== VS Code Integration =====
VSCODE_WORKSPACE_PATH=C:/Users/<USER>/anything llm
VSCODE_TERMINAL_PROFILE=AI Assistant
VSCODE_AUTO_SAVE=true
VSCODE_EXTENSIONS_PATH=.vscode/extensions

# ===== Docker Configuration =====
DOCKER_COMPOSE_FILE=docker-compose.yml
DOCKER_COMPOSE_SIMPLE=docker-compose-simple.yml
DOCKER_NETWORK=ai-integration-network
DOCKER_VOLUME_PREFIX=ai-integration

# ===== Resource Management =====
MAX_MEMORY_USAGE_PERCENT=80
MAX_CPU_USAGE_PERCENT=70
MAX_CONCURRENT_MODELS=3
RESOURCE_CHECK_INTERVAL=300

# ===== Logging Configuration =====
LOG_LEVEL=INFO
LOG_FILE=./logs/ai-integration.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ===== Security Settings =====
API_RATE_LIMIT=100
API_RATE_WINDOW=3600
ALLOWED_ORIGINS=localhost,127.0.0.1
CORS_ENABLED=true

# ===== Workflow Configuration =====
WORKFLOW_TIMEOUT=300
WORKFLOW_RETRY_COUNT=3
WORKFLOW_RETRY_DELAY=5
WORKFLOW_TEMPLATES_PATH=./workflows/templates

# ===== Integration Features =====
ENABLE_AUTO_DISCOVERY=true
ENABLE_HEALTH_CHECKS=true
ENABLE_METRICS_COLLECTION=true
ENABLE_SESSION_PERSISTENCE=true
ENABLE_WORKFLOW_CACHING=true

# ===== Development Settings =====
DEBUG_MODE=false
DEVELOPMENT_MODE=true
TESTING_MODE=false
VERBOSE_LOGGING=false

# ===== Backup and Recovery =====
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=./backups

# ===== Performance Tuning =====
CONNECTION_POOL_SIZE=10
REQUEST_TIMEOUT=30
RESPONSE_CACHE_TTL=300
BATCH_SIZE=50

# ===== Monitoring =====
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=60
ALERT_WEBHOOK_URL=

# ===== External Integrations =====
GITHUB_TOKEN=
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=

# ===== Custom Extensions =====
CUSTOM_PLUGINS_PATH=./plugins
CUSTOM_WORKFLOWS_PATH=./workflows/custom
CUSTOM_AGENTS_PATH=./agents/custom

# ===== Terminal Configuration =====
TERMINAL_DEFAULT=PowerShell
TERMINAL_AI_ASSISTANT_ENABLED=true
TERMINAL_WARP_PATH=C:/Users/<USER>/OneDrive/Desktop/Warp.lnk

# ===== Project Paths =====
PROJECT_ROOT=C:/Users/<USER>/anything llm
DATA_FOLDER=./data
ROUTES_FOLDER=./data/routes
MCP_SERVERS_PATH=C:/Users/<USER>/mcp-servers
DOCKER_MCP_AGENT_PATH=C:/Users/<USER>/docker mcp agent

# ===== Session Management =====
SESSION_TIMEOUT=3600
SESSION_CLEANUP_INTERVAL=1800
SESSION_PERSISTENCE=true
SESSION_ENCRYPTION=false

# ===== Error Handling =====
ERROR_RETRY_COUNT=3
ERROR_RETRY_DELAY=2
ERROR_LOG_DETAILED=true
ERROR_NOTIFICATIONS=true
