# n8n Docker Setup

هذا المشروع يحتوي على إعداد Docker لتشغيل n8n - منصة أتمتة سير العمل.

## المتطلبات

- Docker
- Docker Compose

## التشغيل

### 1. تشغيل n8n

```bash
docker-compose up -d
```

### 2. الوصول إلى n8n

افتح المتصفح واذهب إلى: http://localhost:5678

### 3. تسجيل الدخول

- اسم المستخدم: `admin`
- كلمة المرور: `password123`

## الأوامر المفيدة

### عرض السجلات
```bash
docker-compose logs -f n8n
```

### إيقاف الخدمة
```bash
docker-compose down
```

### إعادة تشغيل الخدمة
```bash
docker-compose restart
```

### تحديث n8n
```bash
docker-compose pull
docker-compose up -d
```

## التخصيص

### تغيير كلمة المرور

1. عدل ملف `.env`
2. غير قيمة `N8N_BASIC_AUTH_PASSWORD`
3. أعد تشغيل الخدمة

### استخدام قاعدة بيانات PostgreSQL

إذا كنت تريد استخدام PostgreSQL بدلاً من SQLite، أضف الخدمة التالية إلى `docker-compose.yml`:

```yaml
  postgres:
    image: postgres:13
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

وغير متغيرات البيئة في خدمة n8n:

```yaml
environment:
  - DB_TYPE=postgresdb
  - DB_POSTGRESDB_HOST=postgres
  - DB_POSTGRESDB_PORT=5432
  - DB_POSTGRESDB_DATABASE=n8n
  - DB_POSTGRESDB_USER=n8n
  - DB_POSTGRESDB_PASSWORD=n8n_password
```

## الأمان

⚠️ **مهم**: تأكد من تغيير كلمة المرور الافتراضية ومفتاح التشفير قبل الاستخدام في بيئة الإنتاج!

## البيانات

جميع بيانات n8n محفوظة في volume باسم `n8n_data` وستبقى محفوظة حتى لو تم حذف الحاوية.

## استكشاف الأخطاء

### إذا لم يعمل n8n:

1. تحقق من السجلات: `docker-compose logs n8n`
2. تأكد من أن المنفذ 5678 غير مستخدم
3. تأكد من تشغيل Docker بشكل صحيح
