#!/usr/bin/env python3
"""
Memory Bridge - جسر الذاكرة المشتركة
ربط الذاكرة بين Augment Agent والمساعدين
"""

import json
import os
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

class MemoryBridge:
    """جسر الذاكرة المشتركة بين Augment والمساعدين"""
    
    def __init__(self):
        self.memory_file = Path("memory/shared-project-memory.json")
        self.session_file = Path("memory/current-session.json")
        self.ensure_memory_structure()
        
    def ensure_memory_structure(self):
        """التأكد من وجود هيكل الذاكرة"""
        memory_dir = Path("memory")
        memory_dir.mkdir(exist_ok=True)
        
        # إنشاء مجلدات فرعية
        subdirs = ["sessions", "agents", "projects", "integration"]
        for subdir in subdirs:
            (memory_dir / subdir).mkdir(exist_ok=True)
    
    def load_memory(self) -> Dict[str, Any]:
        """تحميل الذاكرة المشتركة"""
        if self.memory_file.exists():
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_memory(self, memory_data: Dict[str, Any]):
        """حفظ الذاكرة المشتركة"""
        memory_data["last_updated"] = datetime.now().isoformat()
        with open(self.memory_file, 'w', encoding='utf-8') as f:
            json.dump(memory_data, f, ensure_ascii=False, indent=2)
    
    def log_session_action(self, action: str, participant: str, details: str = ""):
        """تسجيل إجراء في الجلسة الحالية"""
        memory = self.load_memory()
        
        session_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "participant": participant,
            "details": details,
            "session_id": self.get_current_session_id()
        }
        
        if "session_log" not in memory:
            memory["session_log"] = []
        
        memory["session_log"].append(session_entry)
        
        # الاحتفاظ بآخر 100 إدخال فقط
        if len(memory["session_log"]) > 100:
            memory["session_log"] = memory["session_log"][-100:]
        
        self.save_memory(memory)
        return session_entry
    
    def get_current_session_id(self) -> str:
        """الحصول على معرف الجلسة الحالية"""
        return datetime.now().strftime("%Y%m%d_%H%M")
    
    def update_assistant_status(self, assistant_name: str, status: str, details: str = ""):
        """تحديث حالة مساعد"""
        memory = self.load_memory()
        
        if "assistants" not in memory:
            memory["assistants"] = {}
        
        # تحديث حالة المساعد
        if assistant_name == "gemini_cli":
            if "gemini_cli" not in memory["assistants"]:
                memory["assistants"]["gemini_cli"] = {}
            memory["assistants"]["gemini_cli"]["status"] = status
            memory["assistants"]["gemini_cli"]["last_used"] = datetime.now().isoformat()
            if details:
                memory["assistants"]["gemini_cli"]["last_details"] = details
        
        elif assistant_name in ["memory_agent", "file_search_agent", "terminal_agent", "data_analysis_agent"]:
            if "ai_agents" not in memory["assistants"]:
                memory["assistants"]["ai_agents"] = {}
            if assistant_name not in memory["assistants"]["ai_agents"]:
                memory["assistants"]["ai_agents"][assistant_name] = {}
            
            memory["assistants"]["ai_agents"][assistant_name]["status"] = status
            memory["assistants"]["ai_agents"][assistant_name]["last_used"] = datetime.now().isoformat()
            if details:
                memory["assistants"]["ai_agents"][assistant_name]["last_details"] = details
        
        self.save_memory(memory)
        self.log_session_action(f"تحديث حالة {assistant_name}", "Memory Bridge", f"الحالة: {status}")
    
    def add_project_knowledge(self, category: str, key: str, value: Any):
        """إضافة معرفة جديدة للمشروع"""
        memory = self.load_memory()
        
        if "shared_knowledge" not in memory:
            memory["shared_knowledge"] = {}
        
        if category not in memory["shared_knowledge"]:
            memory["shared_knowledge"][category] = {}
        
        memory["shared_knowledge"][category][key] = value
        self.save_memory(memory)
        self.log_session_action(f"إضافة معرفة: {category}.{key}", "Augment Agent", str(value))
    
    def get_project_knowledge(self, category: str = None, key: str = None) -> Any:
        """الحصول على معرفة المشروع"""
        memory = self.load_memory()
        shared_knowledge = memory.get("shared_knowledge", {})
        
        if category is None:
            return shared_knowledge
        
        if key is None:
            return shared_knowledge.get(category, {})
        
        return shared_knowledge.get(category, {}).get(key)
    
    def create_assistant_query_log(self, assistant: str, query: str, response: str):
        """تسجيل استعلام مع مساعد"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "assistant": assistant,
            "query": query,
            "response": response[:500] + "..." if len(response) > 500 else response,
            "session_id": self.get_current_session_id()
        }
        
        # حفظ في ملف منفصل للاستعلامات
        queries_file = Path(f"memory/sessions/queries_{self.get_current_session_id()}.json")
        
        if queries_file.exists():
            with open(queries_file, 'r', encoding='utf-8') as f:
                queries = json.load(f)
        else:
            queries = []
        
        queries.append(log_entry)
        
        with open(queries_file, 'w', encoding='utf-8') as f:
            json.dump(queries, f, ensure_ascii=False, indent=2)
        
        self.log_session_action(f"استعلام {assistant}", "Augment Agent", f"السؤال: {query[:100]}...")
    
    def get_assistant_recommendations(self) -> Dict[str, str]:
        """الحصول على توصيات لاستخدام المساعدين"""
        memory = self.load_memory()
        current_issues = memory.get("project_structure", {}).get("current_issues", [])
        
        recommendations = {}
        
        for issue in current_issues:
            if "embedding" in issue.lower():
                recommendations["anythingllm_issue"] = "استخدم File Search Agent لفحص ملفات التكوين"
            elif "استيراد" in issue or "import" in issue.lower():
                recommendations["import_issue"] = "استخدم Terminal Agent لفحص Python dependencies"
            elif "موارد" in issue or "resource" in issue.lower():
                recommendations["resource_issue"] = "استخدم Data Analysis Agent لتحليل استخدام الموارد"
            elif "منافذ" in issue or "port" in issue.lower():
                recommendations["port_issue"] = "استشر Gemini CLI لتوحيد المنافذ"
        
        return recommendations
    
    def generate_memory_report(self) -> str:
        """إنشاء تقرير الذاكرة المشتركة"""
        memory = self.load_memory()
        
        report = []
        report.append("🧠 تقرير الذاكرة المشتركة")
        report.append("=" * 40)
        
        # معلومات المشروع
        project_name = memory.get("project_name", "غير محدد")
        last_updated = memory.get("last_updated", "غير محدد")
        report.append(f"📁 المشروع: {project_name}")
        report.append(f"🕒 آخر تحديث: {last_updated}")
        
        # حالة المساعدين
        report.append("\n🤖 حالة المساعدين:")
        assistants = memory.get("assistants", {})
        
        # Gemini CLI
        gemini_status = assistants.get("gemini_cli", {}).get("status", "غير محدد")
        report.append(f"  🧠 Gemini CLI: {gemini_status}")
        
        # AI Agents
        ai_agents = assistants.get("ai_agents", {})
        for agent_name, agent_info in ai_agents.items():
            status = agent_info.get("status", "غير محدد")
            report.append(f"  🤖 {agent_name}: {status}")
        
        # آخر الأنشطة
        session_log = memory.get("session_log", [])
        if session_log:
            report.append(f"\n📝 آخر الأنشطة ({len(session_log)} إدخال):")
            for entry in session_log[-5:]:  # آخر 5 أنشطة
                timestamp = entry.get("timestamp", "")[:16]  # YYYY-MM-DD HH:MM
                action = entry.get("action", "")
                participant = entry.get("participant", "")
                report.append(f"  {timestamp} | {participant}: {action}")
        
        # التوصيات
        recommendations = self.get_assistant_recommendations()
        if recommendations:
            report.append("\n💡 التوصيات:")
            for issue, recommendation in recommendations.items():
                report.append(f"  • {recommendation}")
        
        return "\n".join(report)
    
    def sync_with_augment(self, action: str, details: str = ""):
        """مزامنة مع Augment Agent"""
        self.log_session_action(action, "Augment Agent", details)
        
        # تحديث معلومات Augment
        memory = self.load_memory()
        if "augment_agent" not in memory:
            memory["augment_agent"] = {}
        
        memory["augment_agent"]["last_action"] = action
        memory["augment_agent"]["last_action_time"] = datetime.now().isoformat()
        
        self.save_memory(memory)

def main():
    """اختبار نظام الذاكرة"""
    bridge = MemoryBridge()
    
    print("🧠 اختبار نظام الذاكرة المشتركة")
    print("=" * 40)
    
    # تسجيل بعض الأنشطة التجريبية
    bridge.sync_with_augment("بدء اختبار نظام الذاكرة", "اختبار التكامل مع المساعدين")
    bridge.update_assistant_status("gemini_cli", "متاح", "جاهز للاستشارة")
    bridge.update_assistant_status("memory_agent", "يحتاج إصلاح", "مشاكل في الاستيراد")
    
    # إضافة معرفة جديدة
    bridge.add_project_knowledge("testing", "memory_system", "تم اختبار نظام الذاكرة بنجاح")
    
    # إنشاء تقرير
    report = bridge.generate_memory_report()
    print(report)
    
    print("\n✅ تم اختبار نظام الذاكرة بنجاح!")

if __name__ == "__main__":
    main()
