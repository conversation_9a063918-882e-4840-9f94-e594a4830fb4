const express = require('express');
const cors = require('cors');
const axios = require('axios');
const WebSocket = require('ws');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3333;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration
const CONFIG = {
  ollama: {
    url: 'http://***********:11434',
    models: ['llama3:8b', 'phi3:mini', 'gemma3n:e4b', 'mistral:7b']
  },
  gemini: {
    apiKey: process.env.GEMINI_API_KEY,
    url: 'https://generativelanguage.googleapis.com/v1/models'
  },
  n8n: {
    url: 'http://localhost:5678',
    webhook: process.env.N8N_WEBHOOK_URL
  },
  anythingllm: {
    url: 'http://localhost:3001', // AnythingLLM default port
    apiKey: process.env.ANYTHINGLLM_API_KEY
  }
};

// Decision Engine - يحدد أي نموذج يستخدم
class DecisionEngine {
  static chooseModel(request) {
    const { prompt, context, priority, complexity } = request;
    
    // قواعد بسيطة للبداية
    if (priority === 'fast' || prompt.length < 100) {
      return { model: 'ollama', reason: 'Fast response needed' };
    }
    
    if (complexity === 'high' || prompt.includes('research') || prompt.includes('analyze')) {
      return { model: 'gemini', reason: 'Complex analysis required' };
    }
    
    if (context && context.includes('code')) {
      return { model: 'ollama', subModel: 'phi3:mini', reason: 'Code-related task' };
    }
    
    // Default to Ollama for general tasks
    return { model: 'ollama', subModel: 'llama3:8b', reason: 'General purpose' };
  }
}

// Ollama Integration
class OllamaService {
  static async generate(prompt, model = 'llama3:8b') {
    try {
      const response = await axios.post(`${CONFIG.ollama.url}/api/generate`, {
        model: model,
        prompt: prompt,
        stream: false
      });
      
      return {
        success: true,
        response: response.data.response,
        model: model,
        source: 'ollama'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        source: 'ollama'
      };
    }
  }
}

// Gemini Integration
class GeminiService {
  static async generate(prompt) {
    try {
      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${CONFIG.gemini.apiKey}`,
        {
          contents: [{
            parts: [{ text: prompt }]
          }]
        }
      );
      
      return {
        success: true,
        response: response.data.candidates[0].content.parts[0].text,
        model: 'gemini-pro',
        source: 'gemini'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        source: 'gemini'
      };
    }
  }
}

// Main coordination endpoint
app.post('/api/coordinate', async (req, res) => {
  try {
    const { prompt, context, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }
    
    // Decision making
    const decision = DecisionEngine.chooseModel({
      prompt,
      context,
      priority: options.priority,
      complexity: options.complexity
    });
    
    console.log(`🧠 Decision: Using ${decision.model} - ${decision.reason}`);
    
    let result;
    
    // Route to appropriate service
    if (decision.model === 'ollama') {
      result = await OllamaService.generate(prompt, decision.subModel);
    } else if (decision.model === 'gemini') {
      result = await GeminiService.generate(prompt);
    }
    
    // Add decision info to response
    result.decision = decision;
    result.timestamp = new Date().toISOString();
    
    res.json(result);
    
  } catch (error) {
    console.error('Coordination error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Collaborative endpoint - يستخدم عدة نماذج
app.post('/api/collaborate', async (req, res) => {
  try {
    const { prompt, context } = req.body;
    
    console.log('🤝 Starting collaborative response...');
    
    // Get responses from multiple models in parallel
    const [ollamaResult, geminiResult] = await Promise.allSettled([
      OllamaService.generate(prompt, 'llama3:8b'),
      GeminiService.generate(prompt)
    ]);
    
    const responses = [];
    
    if (ollamaResult.status === 'fulfilled' && ollamaResult.value.success) {
      responses.push({
        source: 'ollama',
        model: 'llama3:8b',
        response: ollamaResult.value.response,
        speed: 'fast'
      });
    }
    
    if (geminiResult.status === 'fulfilled' && geminiResult.value.success) {
      responses.push({
        source: 'gemini',
        model: 'gemini-pro',
        response: geminiResult.value.response,
        quality: 'high'
      });
    }
    
    res.json({
      success: true,
      collaborative: true,
      responses: responses,
      summary: `Generated ${responses.length} responses from different models`,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Collaboration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoints
app.get('/api/health', async (req, res) => {
  const health = {
    coordinator: 'healthy',
    timestamp: new Date().toISOString(),
    services: {}
  };
  
  // Check Ollama
  try {
    await axios.get(`${CONFIG.ollama.url}/api/tags`);
    health.services.ollama = 'healthy';
  } catch {
    health.services.ollama = 'unhealthy';
  }
  
  // Check Gemini (simple check)
  health.services.gemini = CONFIG.gemini.apiKey ? 'configured' : 'not configured';
  
  res.json(health);
});

// Test endpoint
app.get('/api/test', async (req, res) => {
  const testPrompt = 'Say hello in Arabic';
  
  try {
    const result = await OllamaService.generate(testPrompt, 'phi3:mini');
    res.json({
      test: 'successful',
      prompt: testPrompt,
      result: result
    });
  } catch (error) {
    res.status(500).json({
      test: 'failed',
      error: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AI Coordinator running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`);
  console.log(`🤝 Coordination: POST http://localhost:${PORT}/api/coordinate`);
  console.log(`🤝 Collaboration: POST http://localhost:${PORT}/api/collaborate`);
});

module.exports = app;
