#!/bin/bash

echo "🐳 Starting AI Coordinator with Docker..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

# Create directories if they don't exist
mkdir -p logs config

# Build and start the containers
echo "📦 Building and starting containers..."
docker-compose up -d --build

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 15

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ AI Coordinator is running successfully!"
    echo ""
    echo "🔗 Services:"
    echo "  - AI Coordinator: http://localhost:3333"
    echo "  - Health Check: http://localhost:3333/api/health"
    echo "  - Redis: localhost:6379"
    echo "  - PostgreSQL: localhost:5432"
    echo ""
    echo "📋 Useful commands:"
    echo "  - View logs: docker-compose logs -f ai-coordinator"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart: docker-compose restart"
    echo "  - Shell access: docker exec -it ai-coordinator sh"
    echo ""
    
    # Test the service
    echo "🧪 Testing the service..."
    sleep 5
    
    if curl -f http://localhost:3333/api/health > /dev/null 2>&1; then
        echo "✅ Service is responding correctly!"
    else
        echo "⚠️  Service might still be starting up. Check logs if needed."
    fi
    
else
    echo "❌ Failed to start AI Coordinator. Check the logs:"
    docker-compose logs
fi
