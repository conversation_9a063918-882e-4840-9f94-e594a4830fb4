#!/bin/bash

echo "🔄 Copying Ollama models from local installation to Docker container..."

# Check if Ollama container is running
if ! docker ps | grep -q "ollama"; then
    echo "❌ Ollama container is not running. Please start it first with:"
    echo "   docker-compose up -d"
    exit 1
fi

# Check if local Ollama installation exists
if ! command -v ollama &> /dev/null; then
    echo "❌ Local Ollama installation not found."
    exit 1
fi

# Get local Ollama models directory
LOCAL_OLLAMA_DIR="$HOME/.ollama"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows path
    LOCAL_OLLAMA_DIR="$USERPROFILE/.ollama"
fi

if [ ! -d "$LOCAL_OLLAMA_DIR" ]; then
    echo "❌ Local Ollama directory not found at: $LOCAL_OLLAMA_DIR"
    exit 1
fi

echo "📂 Local Ollama directory: $LOCAL_OLLAMA_DIR"

# List local models
echo "📋 Local models:"
ollama list

echo ""
echo "🚀 Starting copy process..."

# Copy models directory
if [ -d "$LOCAL_OLLAMA_DIR/models" ]; then
    echo "📦 Copying models..."
    docker cp "$LOCAL_OLLAMA_DIR/models/." ollama:/root/.ollama/models/
    echo "✅ Models copied successfully!"
else
    echo "⚠️  No models directory found locally."
fi

# Copy other Ollama data if exists
if [ -d "$LOCAL_OLLAMA_DIR/blobs" ]; then
    echo "📦 Copying blobs..."
    docker cp "$LOCAL_OLLAMA_DIR/blobs/." ollama:/root/.ollama/blobs/
    echo "✅ Blobs copied successfully!"
fi

# Restart Ollama container to recognize new models
echo "🔄 Restarting Ollama container..."
docker-compose restart ollama

# Wait for container to start
echo "⏳ Waiting for Ollama to start..."
sleep 10

# List models in container
echo "📋 Models in Docker container:"
docker exec ollama ollama list

echo ""
echo "✅ Copy process completed!"
echo "🌐 You can now access Ollama at: http://localhost:11434"
echo "🖥️  Web UI available at: http://localhost:3000"
