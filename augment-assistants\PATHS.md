# 📁 مسارات النظام - System Paths

## 🎯 **المسارات الأساسية**

### **مجلد العمل الرئيسي**
```
C:\Users\<USER>\anything llm\
```

### **مجلد المساعدين المعزول**
```
C:\Users\<USER>\anything llm\augment-assistants\
```

## 📂 **هيكل المجلدات الكامل**

```
C:\Users\<USER>\anything llm\
├── 🤖 augment-assistants\                    ← المساعدين المعزولين
│   ├── README.md
│   ├── PATHS.md                              ← هذا الملف
│   ├── quick-start.py
│   ├── test-simple.py
│   │
│   ├── 📂 shared-memory\                     ← الذاكرة المشتركة
│   │   ├── project-knowledge.json
│   │   ├── session-logs.json
│   │   └── assistants-status.json
│   │
│   ├── 📂 gemini-interface\                  ← واجهة Gemini CLI
│   │   ├── gemini-connector.py
│   │   ├── quick-queries.py
│   │   └── consultation-history.json
│   │
│   ├── 📂 agents-interface\                  ← واجهة AI Agents
│   │   ├── agents-coordinator.py
│   │   ├── task-delegator.py
│   │   └── agents-status.json
│   │
│   ├── 📂 scripts\                          ← سكريبتات مساعدة
│   │   ├── test-assistants.py
│   │   ├── sync-memory.py
│   │   └── health-check.py
│   │
│   └── 📂 logs\                             ← سجلات منفصلة
│       ├── gemini-logs\
│       ├── agents-logs\
│       └── system-logs\
│
├── 🏠 anything-llm\                          ← النظام الكبير (لا نعدل فيه)
├── 🤖 ai-agents\                            ← الوكلاء الأصليين (لا نعدل فيهم)
├── ⚙️ ai-integration-system\                ← نظام التكامل
├── 🧠 memory\                               ← ذاكرة النظام الكبير
└── 💻 .vscode\                              ← إعدادات VS Code
```

## 🔗 **المسارات الخارجية**

### **Gemini CLI**
```
C:\Users\<USER>\gemini-cli\
```

### **مجلد المستخدم الجذر**
```
C:\Users\<USER>\
```

## 🐍 **مسارات Python للاستيراد**

### **من داخل augment-assistants/**
```python
# للوصول للذاكرة المشتركة
from shared_memory.memory_manager import MemoryManager

# للوصول لواجهة Gemini
from gemini_interface.gemini_connector import GeminiConnector

# للوصول لواجهة الوكلاء
from agents_interface.task_delegator import TaskDelegator
```

### **من خارج augment-assistants/**
```python
import sys
sys.path.append('./augment-assistants')

from gemini_interface.gemini_connector import quick_consult
from shared_memory.memory_manager import get_project_status
```

## 💻 **مسارات الأوامر**

### **تشغيل من VS Code Terminal**
```bash
# الانتقال للمساعدين
cd "C:\Users\<USER>\anything llm\augment-assistants"

# تشغيل الاختبار
python test-simple.py

# تشغيل البدء السريع
python quick-start.py
```

### **تشغيل من PowerShell**
```powershell
# الانتقال للمساعدين
Set-Location "C:\Users\<USER>\anything llm\augment-assistants"

# تشغيل الاختبار
python test-simple.py
```

### **تشغيل Gemini CLI**
```bash
# الانتقال لمجلد Gemini
cd "C:\Users\<USER>\Users\mo_as\anything llm\augment-assistants\shared-memory\project-knowledge.json
C:\Users\<USER>\anything llm\.vscode\settings.json
C:\Users\<USER>\anything llm\.vscode\tasks.json
```

### **ملفات الواجهات**
```
C:\Users\<USER>\anything llm\augment-assistants\gemini-interface\gemini-connector.py
C:\Users\<USER>\anything llm\augment-assistants\agents-interface\task-delegator.py
```

### **ملفات السجلات**
```
C:\Users\<USER>\anything llm\augment-assistants\logs\system-logs\
C:\Users\<USER>\anything llm\augment-assistants\gemini-interface\consultation-history.json
```

## 🔄 **مسارات نسبية (Relative Paths)**

### **من داخل augment-assistants/**
```
./shared-memory/project-knowledge.json
./gemini-interface/gemini-connector.py
./scripts/test-assistants.py
../memory/                                   ← ذاكرة النظام الكبير
../ai-agents/                               ← الوكلاء الأصليين
../.vscode/                                 ← إعدادات VS Code
```

### **من مجلد الجذر (anything llm/)**
```
./augment-assistants/shared-memory/project-knowledge.json
./augment-assistants/gemini-interface/gemini-connector.py
./ai-agents/memory-agent.py
./memory/shared-project-memory.json
```

## ⚠️ **قواعد المسارات**

### **✅ آمن للتعديل**
```
C:\Users\<USER>\anything llm\augment-assistants\     ← كل شيء هنا آمن
```

### **❌ ممنوع التعديل**
```
C:\Users\<USER>\anything llm\anything-llm\           ← النظام الكبير
C:\Users\<USER>\anything llm\ai-agents\              ← الوكلاء الأصليين
```

### **⚠️ حذر في التعديل**
```
C:\Users\<USER>\anything llm\.vscode\                ← إعدادات VS Code
C:\Users\<USER>\anything llm\memory\                 ← ذاكرة النظام الكبير
```

## 🧪 **مسارات الاختبار**

### **اختبار المساعدين**
```bash
cd "C:\Users\<USER>\anything llm\augment-assistants"
python test-simple.py
```

### **اختبار Gemini CLI**
```bash
cd "C:\Users\<USER>\Users\mo_as\anything llm"
python ai-agents\simple-test.py
```

## 📋 **نصائح للمسارات**

### **1. استخدم المسارات المطلقة دائماً**
```python
# ✅ جيد
base_path = Path("C:/Users/<USER>/anything llm/augment-assistants")

# ❌ قد يسبب مشاكل
base_path = Path("./augment-assistants")
```

### **2. تحقق من وجود المسار**
```python
if Path("C:/Users/<USER>/anything llm/augment-assistants").exists():
    print("المسار موجود")
else:
    print("المسار غير موجود")
```

### **3. استخدم os.chdir() بحذر**
```python
import os
original_cwd = os.getcwd()
try:
    os.chdir("C:/Users/<USER>/anything llm/augment-assistants")
    # عمل شيء
finally:
    os.chdir(original_cwd)  # العودة للمسار الأصلي
```

## 🔍 **فحص المسارات**

### **سكريبت فحص سريع**
```python
from pathlib import Path

paths_to_check = [
    "C:/Users/<USER>/anything llm/augment-assistants",
    "C:/Users/<USER>/gemini-cli",
    "C:/Users/<USER>/anything llm/ai-agents"
]

for path in paths_to_check:
    status = "✅" if Path(path).exists() else "❌"
    print(f"{status} {path}")
```

---

**📅 تم الإنشاء**: 2025-01-06  
**🔄 آخر تحديث**: 2025-01-06  
**📝 الغرض**: منع أخطاء المسارات في النظام
