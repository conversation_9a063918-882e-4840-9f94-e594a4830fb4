# 🔍 تشخيص شامل للنظام - AI Development Assistant
# ========================================================

Write-Host "🔍 بدء التشخيص الشامل للنظام..." -ForegroundColor Green

# فحص Docker
Write-Host "`n📦 فحص Docker:" -ForegroundColor Cyan
Write-Host "   Docker Version:" -ForegroundColor Yellow
docker --version

Write-Host "   Docker Compose Version:" -ForegroundColor Yellow
docker-compose --version

Write-Host "   Docker Status:" -ForegroundColor Yellow
try {
    docker info --format "{{.ServerVersion}}" 2>$null
    Write-Host "   ✅ Docker يعمل بشكل صحيح" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ Docker غير متاح أو لا يعمل" -ForegroundColor Red
}

# فحص المنافذ
Write-Host "`n🌐 فحص المنافذ:" -ForegroundColor Cyan
$ports = @(4001, 4002, 4003, 11434)
foreach ($port in $ports) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "   ✅ Port ${port} Available" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Port ${port} Not Available" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ Port ${port} Check Error" -ForegroundColor Red
    }
}

# فحص الخدمات
Write-Host "`n🐳 فحص حالة الحاويات:" -ForegroundColor Cyan
try {
    docker-compose ps
}
catch {
    Write-Host "   ❌ لا يمكن الوصول إلى Docker Compose" -ForegroundColor Red
}

# فحص Ollama المحلي
Write-Host "`n🤖 فحص Ollama المحلي:" -ForegroundColor Cyan
try {
    $ollamaProcess = Get-Process "ollama*" -ErrorAction SilentlyContinue
    if ($ollamaProcess) {
        Write-Host "   ✅ Ollama يعمل محلياً" -ForegroundColor Green
        Write-Host "   Process ID: $($ollamaProcess.Id)" -ForegroundColor Yellow
    } else {
        Write-Host "   ❌ Ollama غير مشغل محلياً" -ForegroundColor Red
    }
}
catch {
    Write-Host "   ❌ خطأ في فحص Ollama" -ForegroundColor Red
}

# فحص الملفات المهمة
Write-Host "`n📁 فحص الملفات المهمة:" -ForegroundColor Cyan
$importantFiles = @(
    ".env",
    "docker-compose.yml",
    "configs/anythingllm.env",
    "model_mix/ai-coordinator/Dockerfile"
)

foreach ($file in $importantFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ ${file} Found" -ForegroundColor Green
    } else {
        Write-Host "   ❌ ${file} Missing" -ForegroundColor Red
    }
}

# فحص متغيرات البيئة
Write-Host "`n⚙️ فحص متغيرات البيئة المهمة:" -ForegroundColor Cyan
if (Test-Path ".env") {
    $envContent = Get-Content ".env" -Raw
    $requiredVars = @("AUTH_TOKEN", "JWT_SECRET", "GEMINI_API_KEY", "N8N_ENCRYPTION_KEY")
    
    foreach ($var in $requiredVars) {
        if ($envContent -match "$var=") {
            Write-Host "   ✅ ${var} Set" -ForegroundColor Green
        } else {
            Write-Host "   ❌ ${var} Missing" -ForegroundColor Red
        }
    }
} else {
    Write-Host "   ❌ ملف .env غير موجود" -ForegroundColor Red
}

# فحص مساحة القرص
Write-Host "`n💾 فحص مساحة القرص:" -ForegroundColor Cyan
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
$totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
Write-Host "   المساحة المتاحة: $freeSpaceGB GB من $totalSpaceGB GB" -ForegroundColor Yellow

if ($freeSpaceGB -lt 5) {
    Write-Host "   ⚠️ تحذير: مساحة القرص منخفضة" -ForegroundColor Red
} else {
    Write-Host "   ✅ مساحة القرص كافية" -ForegroundColor Green
}

# ملخص التشخيص
Write-Host "`n📊 ملخص التشخيص:" -ForegroundColor Cyan
Write-Host "   تاريخ التشخيص: $(Get-Date)" -ForegroundColor Yellow
Write-Host "   نظام التشغيل: $($env:OS)" -ForegroundColor Yellow
Write-Host "   اسم الكمبيوتر: $($env:COMPUTERNAME)" -ForegroundColor Yellow
Write-Host "   المستخدم: $($env:USERNAME)" -ForegroundColor Yellow

Write-Host "`n🎯 التوصيات:" -ForegroundColor Green
Write-Host "   1. تأكد من تشغيل Docker Desktop" -ForegroundColor White
Write-Host "   2. تأكد من تشغيل Ollama محلياً" -ForegroundColor White
Write-Host "   3. استخدم scripts/fix-services.ps1 لإصلاح المشاكل" -ForegroundColor White
Write-Host "   4. راجع ملف PROJECT_STRUCTURE.md للبنية الجديدة" -ForegroundColor White
