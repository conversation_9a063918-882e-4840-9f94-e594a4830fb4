#!/bin/bash

# AI Development Assistant - Unified Startup Script
# =================================================
# This script starts all services using the unified docker-compose.yml

echo "🚀 AI Development Assistant - Unified Startup"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
print_status "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi
print_success "Docker is running"

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in current directory"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Using default values."
fi

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose down

# Pull latest images
print_status "Pulling latest Docker images..."
docker-compose pull

# Start services
print_status "Starting all services..."
docker-compose up -d

# Wait a moment for services to start
print_status "Waiting for services to initialize..."
sleep 10

# Check service status
print_status "Checking service status..."
echo ""

# Function to check if a port is responding
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|302\|404"; then
        print_success "$service_name is running on port $port"
        echo "           URL: $url"
    else
        print_warning "$service_name may still be starting on port $port"
        echo "           URL: $url"
    fi
}

# Check each service
echo "📊 Service Status:"
echo "=================="
check_service "AnythingLLM" "3001" "http://localhost:3001"
check_service "Ollama WebUI" "3000" "http://localhost:3000"
check_service "n8n" "5678" "http://localhost:5678"
check_service "Ollama API" "11434" "http://localhost:11434/api/tags"

echo ""
echo "🔗 Quick Access URLs:"
echo "===================="
echo "• AnythingLLM:  http://localhost:3001"
echo "• Ollama WebUI: http://localhost:3000"
echo "• n8n:          http://localhost:5678"
echo "• Ollama API:   http://localhost:11434"

echo ""
echo "📋 Default Credentials:"
echo "======================"
echo "• n8n:          admin / password123"
echo "• AnythingLLM:  (configured in .env)"

echo ""
echo "🛠️ Useful Commands:"
echo "=================="
echo "• View logs:        docker-compose logs -f [service_name]"
echo "• Stop all:         docker-compose down"
echo "• Restart service:  docker-compose restart [service_name]"
echo "• View status:      docker-compose ps"

echo ""
print_success "All services started successfully!"
print_status "Check the URLs above to access your services."

# Optional: Open browser (uncomment if desired)
# print_status "Opening AnythingLLM in browser..."
# if command -v xdg-open > /dev/null; then
#     xdg-open http://localhost:3001
# elif command -v open > /dev/null; then
#     open http://localhost:3001
# fi
