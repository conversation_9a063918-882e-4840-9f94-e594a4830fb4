#!/usr/bin/env python3
"""
سكريبت لتحميل workflow إلى n8n
"""

import requests
import json
import base64
from urllib.parse import urljoin

# إعدادات n8n
N8N_BASE_URL = "http://localhost:5678"
N8N_USERNAME = "<EMAIL>"
N8N_PASSWORD = "2452329511@Amr"

def authenticate_n8n():
    """تسجيل الدخول إلى n8n والحصول على session cookie"""
    login_url = urljoin(N8N_BASE_URL, "/rest/login")
    
    login_data = {
        "emailOrLdapLoginId": N8N_USERNAME,
        "password": N8N_PASSWORD
    }
    
    session = requests.Session()
    
    try:
        response = session.post(login_url, json=login_data)
        if response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح إلى n8n")
            return session
        else:
            print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ n8n: {e}")
        return None

def upload_workflow(session, workflow_file):
    """تحميل workflow إلى n8n"""
    
    # قراءة ملف الـ workflow
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الـ workflow: {e}")
        return False
    
    # رابط API لإنشاء workflow جديد
    create_url = urljoin(N8N_BASE_URL, "/rest/workflows")
    
    try:
        response = session.post(create_url, json=workflow_data)
        
        if response.status_code == 200 or response.status_code == 201:
            result = response.json()
            print(f"✅ تم تحميل الـ workflow بنجاح!")
            print(f"📋 اسم الـ workflow: {result.get('name', 'غير محدد')}")
            print(f"🆔 معرف الـ workflow: {result.get('id', 'غير محدد')}")
            return True
        else:
            print(f"❌ فشل في تحميل الـ workflow: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحميل الـ workflow: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تحميل AI Dev Assistant Workflow إلى n8n...")
    
    # تسجيل الدخول
    session = authenticate_n8n()
    if not session:
        return
    
    # تحميل الـ workflow
    workflow_file = "ai_dev_assistant_workflow.json"
    success = upload_workflow(session, workflow_file)
    
    if success:
        print("\n🎉 تم تحميل الـ workflow بنجاح!")
        print(f"🌐 يمكنك الوصول إلى n8n على: {N8N_BASE_URL}")
        print("📝 لا تنس إعداد credentials للـ Google Gemini API")
    else:
        print("\n❌ فشل في تحميل الـ workflow")

if __name__ == "__main__":
    main()
