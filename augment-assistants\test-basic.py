#!/usr/bin/env python3
"""
اختبار أساسي للمساعدين
"""

import os
import json

def test_basic_structure():
    """اختبار الهيكل الأساسي"""
    print("🔍 اختبار الهيكل الأساسي...")
    
    # فحص الملفات الأساسية
    files = ["README.md", "PATHS.md", "quick-start.py"]
    dirs = ["shared-memory", "gemini-interface", "agents-interface", "scripts", "logs"]
    
    print("\n📄 الملفات:")
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
    
    print("\n📂 المجلدات:")
    for dir in dirs:
        if os.path.exists(dir):
            print(f"✅ {dir}/")
        else:
            print(f"❌ {dir}/")

def test_memory_file():
    """اختبار ملف الذاكرة"""
    print("\n🧠 اختبار ملف الذاكرة...")
    
    memory_file = "shared-memory/project-knowledge.json"
    if os.path.exists(memory_file):
        print("✅ ملف الذاكرة موجود")
        try:
            with open(memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("✅ ملف الذاكرة صالح")
            print(f"📁 اسم النظام: {data.get('system_info', {}).get('name', 'غير محدد')}")
            return True
        except Exception as e:
            print(f"❌ خطأ في قراءة الذاكرة: {str(e)}")
            return False
    else:
        print("❌ ملف الذاكرة غير موجود")
        return False

def test_gemini_simple():
    """اختبار Gemini بسيط"""
    print("\n🧠 اختبار Gemini CLI...")
    
    try:
        import subprocess
        
        # تغيير المجلد
        original_cwd = os.getcwd()
        os.chdir("C:/Users/<USER>")
        
        # اختبار بسيط
        result = subprocess.run(
            ["gemini", "--version"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        os.chdir(original_cwd)
        
        if result.returncode == 0:
            print("✅ Gemini CLI متاح")
            return True
        else:
            print("❌ Gemini CLI غير متاح")
            return False
            
    except FileNotFoundError:
        print("❌ Gemini CLI غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في Gemini: {str(e)}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🤖 اختبار أساسي للمساعدين المعزولين")
    print("=" * 50)
    
    # تغيير المجلد للمساعدين
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📍 مجلد العمل: {os.getcwd()}")
    
    # تشغيل الاختبارات
    test_basic_structure()
    memory_ok = test_memory_file()
    gemini_ok = test_gemini_simple()
    
    # النتيجة
    print(f"\n📊 النتيجة:")
    print(f"🧠 الذاكرة: {'✅ تعمل' if memory_ok else '❌ لا تعمل'}")
    print(f"🧠 Gemini CLI: {'✅ متاح' if gemini_ok else '❌ غير متاح'}")
    
    if memory_ok:
        print(f"\n🎉 النظام المعزول جاهز للاستخدام!")
        print(f"💡 يمكنك الآن استخدام المساعدين بأمان")
    else:
        print(f"\n⚠️ هناك مشاكل في النظام")

if __name__ == "__main__":
    main()
