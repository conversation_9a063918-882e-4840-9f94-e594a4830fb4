#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار مبسط للوكلاء
"""

import requests
import json
import sys
import os

def test_ollama():
    """اختبار Ollama"""
    print("🔍 اختبار Ollama...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama متصل - النماذج: {len(models)}")
            return True
        else:
            print(f"❌ Ollama غير متاح: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ Ollama: {e}")
        return False

def test_simple_generation():
    """اختبار توليد بسيط"""
    print("🧪 اختبار التوليد...")
    try:
        data = {
            "model": "phi3:mini",
            "prompt": "Hello",
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json().get("response", "")
            print(f"✅ التوليد نجح: {result[:30]}...")
            return True
        else:
            print(f"❌ التوليد فشل: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ التوليد: {e}")
        return False

def test_memory_agent():
    """اختبار وكيل الذاكرة"""
    print("🧠 اختبار Memory Agent...")
    try:
        # استيراد محلي
        sys.path.append('.')
        from memory_agent import MemoryAgent
        
        agent = MemoryAgent()
        report = agent.generate_session_report()
        sessions = report.get('total_sessions', 0)
        print(f"✅ Memory Agent: {sessions} جلسات")
        return True
    except Exception as e:
        print(f"❌ Memory Agent: {e}")
        return False

def test_file_search_agent():
    """اختبار وكيل البحث"""
    print("🔍 اختبار File Search Agent...")
    try:
        sys.path.append('.')
        from file_search_agent import FileSearchAgent
        
        agent = FileSearchAgent()
        files = agent.scan_directory('.', max_files=3)
        print(f"✅ File Search Agent: {len(files)} ملفات")
        return True
    except Exception as e:
        print(f"❌ File Search Agent: {e}")
        return False

def test_terminal_agent():
    """اختبار وكيل الترمينال"""
    print("💻 اختبار Terminal Agent...")
    try:
        sys.path.append('.')
        from terminal_agent import TerminalAgent
        
        agent = TerminalAgent()
        info = agent.get_system_info()
        if "error" not in info:
            os_name = info.get('os', {}).get('system', 'Unknown')
            print(f"✅ Terminal Agent: {os_name}")
            return True
        else:
            print(f"❌ Terminal Agent: {info['error']}")
            return False
    except Exception as e:
        print(f"❌ Terminal Agent: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🤖 اختبار الوكلاء الذكيين")
    print("=" * 40)
    
    tests = [
        ("Ollama", test_ollama),
        ("التوليد", test_simple_generation),
        ("Memory Agent", test_memory_agent),
        ("File Search Agent", test_file_search_agent),
        ("Terminal Agent", test_terminal_agent)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في {name}: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 النتيجة: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الوكلاء تعمل!")
    elif passed > total // 2:
        print("⚠️ معظم الوكلاء تعمل")
    else:
        print("🚨 مشاكل في النظام")

if __name__ == "__main__":
    main()
