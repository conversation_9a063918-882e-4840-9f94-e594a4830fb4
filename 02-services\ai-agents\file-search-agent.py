#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 File Search Agent - AI Development Assistant
===============================================
وكيل متخصص في البحث والتحليل في الملفات باستخدام Ollama

المميزات:
- البحث الذكي في الملفات
- تحليل محتوى الكود
- استخراج الوظائف والكلاسات
- تحليل التبعيات
- فهم هيكل المشروع
- البحث الدلالي

النموذج المستخدم: llama3:8b (متوازن للتحليل)
"""

import json
import requests
import os
import re
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import fnmatch

class FileSearchAgent:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        تهيئة وكيل البحث في الملفات
        
        Args:
            ollama_url: رابط خادم Ollama
        """
        self.ollama_url = ollama_url
        self.model = "llama3:8b"  # نموذج متوازن للتحليل
        
        # أنواع الملفات المدعومة
        self.supported_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.less', '.xml', '.json', '.yaml', '.yml',
            '.md', '.txt', '.sql', '.sh', '.bat', '.ps1', '.dockerfile',
            '.env', '.ini', '.conf', '.cfg'
        }
        
        # أنماط الاستبعاد
        self.exclude_patterns = [
            '*/node_modules/*', '*/.git/*', '*/venv/*', '*/__pycache__/*',
            '*/dist/*', '*/build/*', '*.pyc', '*.log', '*.tmp'
        ]
        
        print(f"🔍 File Search Agent initialized with model: {self.model}")
    
    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """
        استدعاء نموذج Ollama
        
        Args:
            prompt: النص المطلوب معالجته
            system_prompt: تعليمات النظام
            
        Returns:
            الرد من النموذج
        """
        try:
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                return f"خطأ في الاتصال: {response.status_code}"
                
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def _should_exclude(self, file_path: str) -> bool:
        """
        فحص ما إذا كان يجب استبعاد الملف
        
        Args:
            file_path: مسار الملف
            
        Returns:
            True إذا كان يجب الاستبعاد
        """
        for pattern in self.exclude_patterns:
            if fnmatch.fnmatch(file_path, pattern):
                return True
        return False
    
    def scan_directory(self, directory: str, max_files: int = 100) -> List[Dict[str, Any]]:
        """
        مسح مجلد وفهرسة الملفات
        
        Args:
            directory: مسار المجلد
            max_files: الحد الأقصى للملفات
            
        Returns:
            قائمة معلومات الملفات
        """
        files_info = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            return []
        
        for file_path in directory_path.rglob('*'):
            if len(files_info) >= max_files:
                break
                
            if not file_path.is_file():
                continue
                
            if self._should_exclude(str(file_path)):
                continue
                
            if file_path.suffix.lower() not in self.supported_extensions:
                continue
            
            try:
                stat = file_path.stat()
                files_info.append({
                    "path": str(file_path),
                    "name": file_path.name,
                    "extension": file_path.suffix,
                    "size": stat.st_size,
                    "modified": stat.st_mtime,
                    "relative_path": str(file_path.relative_to(directory_path))
                })
            except Exception:
                continue
        
        return files_info
    
    def analyze_code_file(self, file_path: str) -> Dict[str, Any]:
        """
        تحليل ملف كود
        
        Args:
            file_path: مسار الملف
            
        Returns:
            تحليل شامل للملف
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            file_ext = Path(file_path).suffix.lower()
            
            # تحديد نوع التحليل حسب نوع الملف
            if file_ext in ['.py']:
                return self._analyze_python_file(content, file_path)
            elif file_ext in ['.js', '.ts', '.jsx', '.tsx']:
                return self._analyze_javascript_file(content, file_path)
            elif file_ext in ['.json', '.yaml', '.yml']:
                return self._analyze_config_file(content, file_path)
            else:
                return self._analyze_generic_file(content, file_path)
                
        except Exception as e:
            return {"error": str(e), "file": file_path}
    
    def _analyze_python_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """تحليل ملف Python"""
        system_prompt = """أنت محلل كود Python خبير. حلل الكود وأستخرج:
1. الكلاسات والوظائف الرئيسية
2. المكتبات المستوردة
3. الغرض من الملف
4. نقاط التحسين المحتملة
5. التبعيات

اجعل التحليل مفصل ومفيد."""

        analysis = self._call_ollama(f"حلل هذا الكود Python:\n\n{content[:2000]}", system_prompt)
        
        # استخراج معلومات إضافية
        imports = re.findall(r'^(?:from\s+\S+\s+)?import\s+(.+)$', content, re.MULTILINE)
        classes = re.findall(r'^class\s+(\w+)', content, re.MULTILINE)
        functions = re.findall(r'^def\s+(\w+)', content, re.MULTILINE)
        
        return {
            "file": file_path,
            "type": "python",
            "analysis": analysis,
            "imports": imports,
            "classes": classes,
            "functions": functions,
            "lines": len(content.splitlines()),
            "chars": len(content)
        }
    
    def _analyze_javascript_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """تحليل ملف JavaScript/TypeScript"""
        system_prompt = """أنت محلل كود JavaScript/TypeScript خبير. حلل الكود وأستخرج:
1. الوظائف والكومبوننتس الرئيسية
2. المكتبات المستوردة
3. الغرض من الملف
4. نوع المشروع (React, Node.js, etc.)
5. التبعيات

اجعل التحليل مفصل ومفيد."""

        analysis = self._call_ollama(f"حلل هذا الكود JavaScript:\n\n{content[:2000]}", system_prompt)
        
        # استخراج معلومات إضافية
        imports = re.findall(r'import\s+.*?from\s+[\'"](.+?)[\'"]', content)
        functions = re.findall(r'(?:function\s+(\w+)|const\s+(\w+)\s*=.*?=>)', content)
        
        return {
            "file": file_path,
            "type": "javascript",
            "analysis": analysis,
            "imports": imports,
            "functions": [f for f in functions if f],
            "lines": len(content.splitlines()),
            "chars": len(content)
        }
    
    def _analyze_config_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """تحليل ملف تكوين"""
        system_prompt = """أنت محلل ملفات التكوين. حلل المحتوى وأستخرج:
1. نوع التكوين
2. الإعدادات الرئيسية
3. الغرض من الملف
4. التبعيات المذكورة
5. نقاط مهمة للمطور

اجعل التحليل واضح ومفيد."""

        analysis = self._call_ollama(f"حلل ملف التكوين:\n\n{content[:1500]}", system_prompt)
        
        return {
            "file": file_path,
            "type": "config",
            "analysis": analysis,
            "lines": len(content.splitlines()),
            "chars": len(content)
        }
    
    def _analyze_generic_file(self, content: str, file_path: str) -> Dict[str, Any]:
        """تحليل ملف عام"""
        system_prompt = """حلل هذا الملف وأستخرج:
1. نوع المحتوى
2. الغرض المحتمل
3. المعلومات المهمة
4. العلاقة بالمشروع

اجعل التحليل مفيد للمطور."""

        analysis = self._call_ollama(f"حلل هذا الملف:\n\n{content[:1500]}", system_prompt)
        
        return {
            "file": file_path,
            "type": "generic",
            "analysis": analysis,
            "lines": len(content.splitlines()),
            "chars": len(content)
        }
    
    def search_in_files(self, query: str, directory: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        البحث في الملفات
        
        Args:
            query: استعلام البحث
            directory: مجلد البحث
            max_results: عدد النتائج المطلوبة
            
        Returns:
            نتائج البحث مع التقييم
        """
        results = []
        files = self.scan_directory(directory, max_files=50)
        
        for file_info in files:
            try:
                with open(file_info["path"], 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # البحث النصي البسيط
                if query.lower() in content.lower():
                    # تقييم الصلة باستخدام AI
                    system_prompt = f"""قيم مدى صلة هذا الملف بالاستعلام: "{query}"
أعط درجة من 0 إلى 10 وأذكر السبب."""

                    relevance = self._call_ollama(
                        f"الملف: {file_info['name']}\nالمحتوى:\n{content[:1000]}",
                        system_prompt
                    )
                    
                    # استخراج الدرجة
                    score_match = re.search(r'(\d+)', relevance)
                    score = int(score_match.group(1)) if score_match else 5
                    
                    results.append({
                        "file": file_info["path"],
                        "name": file_info["name"],
                        "score": score,
                        "relevance": relevance,
                        "size": file_info["size"],
                        "preview": content[:300] + "..."
                    })
                    
            except Exception:
                continue
        
        # ترتيب النتائج
        results.sort(key=lambda x: x["score"], reverse=True)
        return results[:max_results]
    
    def generate_project_summary(self, directory: str) -> Dict[str, Any]:
        """
        إنشاء ملخص شامل للمشروع
        
        Args:
            directory: مجلد المشروع
            
        Returns:
            ملخص تفصيلي للمشروع
        """
        files = self.scan_directory(directory, max_files=30)
        
        # تحليل أنواع الملفات
        file_types = {}
        total_size = 0
        
        for file_info in files:
            ext = file_info["extension"]
            file_types[ext] = file_types.get(ext, 0) + 1
            total_size += file_info["size"]
        
        # تحليل عينة من الملفات
        sample_analyses = []
        for file_info in files[:5]:  # أول 5 ملفات
            analysis = self.analyze_code_file(file_info["path"])
            if "error" not in analysis:
                sample_analyses.append(analysis)
        
        # إنشاء ملخص عام
        system_prompt = """أنت محلل مشاريع البرمجة. بناءً على المعلومات المقدمة، 
اكتب ملخص شامل عن المشروع يتضمن:
1. نوع المشروع
2. التقنيات المستخدمة
3. الهيكل العام
4. الغرض المحتمل
5. نقاط القوة والضعف"""

        project_info = f"""
إجمالي الملفات: {len(files)}
أنواع الملفات: {file_types}
الحجم الإجمالي: {total_size} بايت

تحليلات عينة:
{json.dumps(sample_analyses, indent=2, ensure_ascii=False)}
"""

        summary = self._call_ollama(project_info, system_prompt)
        
        return {
            "directory": directory,
            "total_files": len(files),
            "file_types": file_types,
            "total_size": total_size,
            "summary": summary,
            "sample_analyses": sample_analyses
        }

def main():
    """دالة الاختبار الرئيسية"""
    print("🔍 File Search Agent - AI Development Assistant")
    print("=" * 50)
    
    # تهيئة الوكيل
    agent = FileSearchAgent()
    
    # مسح المجلد الحالي
    print("\n📁 مسح المجلد الحالي:")
    files = agent.scan_directory(".", max_files=10)
    for file_info in files[:5]:
        print(f"- {file_info['name']} ({file_info['size']} بايت)")
    
    # تحليل ملف
    if files:
        print(f"\n🔍 تحليل الملف: {files[0]['name']}")
        analysis = agent.analyze_code_file(files[0]['path'])
        if "analysis" in analysis:
            print(analysis["analysis"][:200] + "...")

if __name__ == "__main__":
    main()
