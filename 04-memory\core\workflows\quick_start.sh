#!/bin/bash

# 🚀 سكريبت البدء السريع للنظام الذكي المتكامل
# Quick Start Script for AI Integrated System

echo "🤖 مرحباً بك في النظام الذكي المتكامل للتطوير"
echo "=================================================="

# ألوان للإخراج
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# فحص المتطلبات الأساسية
echo "🔍 فحص المتطلبات الأساسية..."

# فحص Docker
if command -v docker &> /dev/null; then
    print_status "Docker مثبت"
else
    print_error "Docker غير مثبت. يرجى تثبيت Docker Desktop أولاً"
    exit 1
fi

# فحص Docker Compose
if command -v docker-compose &> /dev/null; then
    print_status "Docker Compose مثبت"
else
    print_error "Docker Compose غير مثبت"
    exit 1
fi

# فحص Python
if command -v python3 &> /dev/null; then
    print_status "Python مثبت"
else
    print_error "Python غير مثبت. يرجى تثبيت Python 3.8+ أولاً"
    exit 1
fi

# التحقق من تشغيل Docker
if ! docker info &> /dev/null; then
    print_error "Docker غير مُفعل. يرجى تشغيل Docker Desktop أولاً"
    exit 1
fi

print_status "جميع المتطلبات متوفرة!"

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."
mkdir -p anything_llm_config
mkdir -p n8n_workflows
mkdir -p .vscode
print_status "المجلدات جاهزة"

# تثبيت المكتبات المطلوبة لـ Python
echo "📦 تثبيت مكتبات Python..."
pip3 install requests > /dev/null 2>&1
print_status "مكتبات Python مثبتة"

# تشغيل النظام
echo "🚀 تشغيل النظام الذكي..."
docker-compose up -d

# انتظار تشغيل الخدمات
echo "⏳ انتظار تشغيل الخدمات..."
sleep 10

# فحص حالة الخدمات
echo "🔍 فحص حالة الخدمات..."

services=("n8n_orchestrator" "ollama_local_models" "anything_llm_commander")
all_running=true

for service in "${services[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$service"; then
        print_status "$service يعمل بنجاح"
    else
        print_warning "$service لا يعمل بشكل صحيح"
        all_running=false
    fi
done

if [ "$all_running" = true ]; then
    print_status "جميع الخدمات تعمل بنجاح!"
else
    print_warning "بعض الخدمات لا تعمل. جاري المحاولة مرة أخرى..."
    docker-compose restart
    sleep 5
fi

# تحميل النماذج الأساسية
echo "📥 تحميل النماذج الأساسية..."
print_info "هذا قد يستغرق بضع دقائق في المرة الأولى..."

# تحميل llama3 إذا لم يكن موجوداً
if ! docker-compose exec -T ollama ollama list | grep -q "llama3"; then
    echo "تحميل llama3:8b..."
    docker-compose exec -T ollama ollama pull llama3:8b
    print_status "llama3:8b تم تحميله"
fi

# تحميل mistral إذا لم يكن موجوداً
if ! docker-compose exec -T ollama ollama list | grep -q "mistral"; then
    echo "تحميل mistral..."
    docker-compose exec -T ollama ollama pull mistral
    print_status "mistral تم تحميله"
fi

# اختبار النظام
echo "🧪 اختبار النظام..."
if python3 vscode_ai_controller.py --check-status > /dev/null 2>&1; then
    print_status "النظام يعمل بشكل صحيح!"
else
    print_warning "هناك مشكلة في النظام، ولكن يمكنك المتابعة"
fi

# طباعة معلومات الوصول
echo ""
echo "🎉 النظام جاهز للاستخدام!"
echo "================================"
echo ""
echo "📋 روابط الوصول:"
echo "• AnythingLLM: http://localhost:3001"
echo "• n8n: http://localhost:5678 (admin/admin123)"
echo "• Ollama API: http://localhost:11434"
echo ""
echo "🎮 طرق الاستخدام:"
echo "1. من الطرفية:"
echo "   python3 vscode_ai_controller.py 'اكتب دالة Python بسيطة'"
echo ""
echo "2. من VS Code:"
echo "   Ctrl+Shift+P > Tasks: Run Task > 🤖 طلب بسيط من AI"
echo ""
echo "3. من AnythingLLM:"
echo "   افتح المتصفح على http://localhost:3001"
echo ""
echo "📚 للمزيد من المعلومات:"
echo "   اقرأ ملف README.md"
echo ""

# اختبار بسيط
echo "🔬 اختبار سريع..."
read -p "هل تريد إجراء اختبار سريع؟ (y/n): " test_choice

if [[ $test_choice == "y" || $test_choice == "Y" ]]; then
    echo "إجراء اختبار بسيط..."
    python3 vscode_ai_controller.py "قل مرحباً" 2>/dev/null
    if [ $? -eq 0 ]; then
        print_status "الاختبار نجح!"
    else
        print_warning "الاختبار فشل، لكن النظام قد يعمل"
    fi
fi

# اقتراح الخطوات التالية
echo ""
echo "🚀 الخطوات التالية المقترحة:"
echo "1. افتح VS Code في هذا المجلد"
echo "2. جرب أوامر VS Code Tasks (Ctrl+Shift+P)"
echo "3. ارفع ملفات مشروعك إلى AnythingLLM"
echo "4. إذا كان لديك Google Cloud، اتبع google_cloud_setup.md"
echo ""

# إنشاء ملف .env للإعدادات
if [ ! -f .env ]; then
    echo "📝 إنشاء ملف الإعدادات..."
    cat > .env << EOF
# إعدادات النظام الذكي المتكامل
OLLAMA_URL=http://localhost:11434
N8N_URL=http://localhost:5678
ANYTHING_LLM_URL=http://localhost:3001

# حدود الاستخدام اليومي
DAILY_COST_LIMIT=5.0
DAILY_TOKEN_LIMIT=10000

# إعدادات Google Cloud (اختيارية)
# GOOGLE_CLOUD_PROJECT=ai-integrated-system-2025
# GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
EOF
    print_status "ملف .env تم إنشاؤه"
fi

print_status "البدء السريع مكتمل! استمتع بالنظام الذكي 🎉"

# فتح المتصفح (اختياري)
read -p "هل تريد فتح واجهة AnythingLLM في المتصفح؟ (y/n): " browser_choice

if [[ $browser_choice == "y" || $browser_choice == "Y" ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3001
    elif command -v open &> /dev/null; then
        open http://localhost:3001
    elif command -v start &> /dev/null; then
        start http://localhost:3001
    else
        print_info "افتح المتصفح يدوياً على: http://localhost:3001"
    fi
fi

echo "🏁 انتهى البدء السريع بنجاح!"
