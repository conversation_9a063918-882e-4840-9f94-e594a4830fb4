#!/bin/bash

echo "🚀 Starting n8n with Docker..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

# Start n8n
echo "📦 Starting n8n container..."
docker-compose up -d

# Wait a moment for the container to start
sleep 5

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo "✅ n8n is running successfully!"
    echo "🌐 Access n8n at: http://localhost:5678"
    echo "👤 Username: admin"
    echo "🔑 Password: password123"
    echo ""
    echo "📋 Useful commands:"
    echo "  - View logs: docker-compose logs -f n8n"
    echo "  - Stop n8n: docker-compose down"
    echo "  - Restart n8n: docker-compose restart"
else
    echo "❌ Failed to start n8n. Check the logs:"
    docker-compose logs n8n
fi
