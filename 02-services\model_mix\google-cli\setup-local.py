#!/usr/bin/env python3
"""
إعداد Google Gemini للعمل مع التثبيت المحلي
Local Google Gemini Setup Script
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_packages():
    """التحقق من المكتبات المطلوبة وتثبيتها"""
    required_packages = [
        'google-generativeai',
        'google-cloud-aiplatform',
        'google-auth',
        'requests'
    ]
    
    print("🔍 التحقق من المكتبات المطلوبة...")
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} مثبت")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير مثبت")
    
    if missing_packages:
        print(f"\n📦 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ تم تثبيت جميع المكتبات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المكتبات")
            return False
    
    return True

def check_gcloud_cli():
    """التحقق من Google Cloud CLI"""
    try:
        result = subprocess.run(['gcloud', 'version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Google Cloud CLI مثبت ويعمل")
            print(f"الإصدار: {result.stdout.split()[2]}")
            return True
        else:
            print("❌ Google Cloud CLI غير مثبت أو لا يعمل")
            return False
    except FileNotFoundError:
        print("❌ Google Cloud CLI غير موجود في PATH")
        return False

def setup_environment():
    """إعداد ملف البيئة المحلي"""
    env_file = Path('.env.local')
    
    if env_file.exists():
        print("✅ ملف .env.local موجود")
        return True
    
    print("📝 إنشاء ملف .env.local")
    
    env_content = """# Google Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# Google Cloud Project
GOOGLE_CLOUD_PROJECT=your-project-id

# Optional: Service Account
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Local Configuration
PYTHONPATH=./scripts
"""
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✅ تم إنشاء {env_file}")
    print("⚠️  يرجى تعديل الملف وإضافة مفاتيح API الخاصة بك")
    
    return True

def create_local_test_script():
    """إنشاء سكريبت اختبار محلي"""
    test_script = Path('test-gemini-local.py')
    
    script_content = '''#!/usr/bin/env python3
"""
اختبار Gemini API محلياً
Local Gemini API Test
"""

import os
import sys
from pathlib import Path

# تحميل متغيرات البيئة من .env.local
def load_env():
    env_file = Path('.env.local')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

load_env()

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
except ImportError:
    print("❌ المكتبة غير مثبتة. قم بتشغيل: pip install google-generativeai")
    sys.exit(1)

def test_gemini():
    """اختبار Gemini API"""
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ يرجى تعيين GEMINI_API_KEY في ملف .env.local")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        # اختبار النماذج المتاحة
        print("📋 النماذج المتاحة:")
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"  - {model.name}")
        
        # اختبار توليد النص
        model = genai.GenerativeModel('gemini-pro')
        response = model.generate_content("قل مرحبا باللغة العربية")
        
        print("\\n🤖 اختبار Gemini:")
        print(f"السؤال: قل مرحبا باللغة العربية")
        print(f"الإجابة: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Gemini: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار Gemini API محلياً")
    print("=" * 40)
    
    if test_gemini():
        print("\\n✅ الاختبار نجح!")
    else:
        print("\\n❌ الاختبار فشل!")
'''
    
    with open(test_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # جعل الملف قابل للتنفيذ
    os.chmod(test_script, 0o755)
    
    print(f"✅ تم إنشاء {test_script}")
    return True

def create_gemini_cli():
    """إنشاء أداة سطر أوامر لـ Gemini"""
    cli_script = Path('gemini-cli.py')
    
    cli_content = '''#!/usr/bin/env python3
"""
أداة سطر أوامر لـ Google Gemini
Gemini Command Line Interface
"""

import os
import sys
import argparse
from pathlib import Path

# تحميل متغيرات البيئة
def load_env():
    env_file = Path('.env.local')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

load_env()

try:
    import google.generativeai as genai
except ImportError:
    print("❌ المكتبة غير مثبتة. قم بتشغيل: pip install google-generativeai")
    sys.exit(1)

def setup_gemini():
    """إعداد Gemini API"""
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not api_key or api_key == 'your-gemini-api-key-here':
        print("❌ يرجى تعيين GEMINI_API_KEY في ملف .env.local")
        return None
    
    genai.configure(api_key=api_key)
    return genai.GenerativeModel('gemini-pro')

def chat_mode(model):
    """وضع المحادثة التفاعلية"""
    print("💬 وضع المحادثة التفاعلية (اكتب 'exit' للخروج)")
    print("-" * 50)
    
    chat = model.start_chat(history=[])
    
    while True:
        try:
            user_input = input("\\n👤 أنت: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'خروج']:
                print("👋 وداعاً!")
                break
            
            if not user_input:
                continue
            
            print("🤖 Gemini: ", end="", flush=True)
            response = chat.send_message(user_input)
            print(response.text)
            
        except KeyboardInterrupt:
            print("\\n\\n👋 تم إيقاف المحادثة")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

def single_prompt(model, prompt):
    """سؤال واحد"""
    try:
        response = model.generate_content(prompt)
        print("🤖 Gemini:")
        print(response.text)
    except Exception as e:
        print(f"❌ خطأ: {e}")

def main():
    parser = argparse.ArgumentParser(description='أداة سطر أوامر لـ Google Gemini')
    parser.add_argument('--chat', '-c', action='store_true', 
                       help='وضع المحادثة التفاعلية')
    parser.add_argument('--prompt', '-p', type=str, 
                       help='سؤال واحد')
    parser.add_argument('--models', '-m', action='store_true',
                       help='عرض النماذج المتاحة')
    
    args = parser.parse_args()
    
    model = setup_gemini()
    if not model:
        return
    
    if args.models:
        print("📋 النماذج المتاحة:")
        for m in genai.list_models():
            if 'generateContent' in m.supported_generation_methods:
                print(f"  - {m.name}")
        return
    
    if args.chat:
        chat_mode(model)
    elif args.prompt:
        single_prompt(model, args.prompt)
    else:
        print("استخدم --help لعرض الخيارات المتاحة")

if __name__ == "__main__":
    main()
'''
    
    with open(cli_script, 'w', encoding='utf-8') as f:
        f.write(cli_content)
    
    os.chmod(cli_script, 0o755)
    
    print(f"✅ تم إنشاء {cli_script}")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد Google Gemini للعمل المحلي")
    print("=" * 50)
    
    # التحقق من المكتبات
    if not check_python_packages():
        return
    
    # التحقق من Google Cloud CLI
    check_gcloud_cli()
    
    # إعداد البيئة
    setup_environment()
    
    # إنشاء سكريبتات الاختبار
    create_local_test_script()
    create_gemini_cli()
    
    print("\n🎉 تم الإعداد بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. عدل ملف .env.local وأضف مفتاح Gemini API")
    print("2. شغل: python test-gemini-local.py")
    print("3. استخدم: python gemini-cli.py --chat للمحادثة")
    print("4. أو: python gemini-cli.py --prompt 'سؤالك هنا'")

if __name__ == "__main__":
    main()
