#!/usr/bin/env python3
"""
Augment Assistants - Quick Start
بدء سريع للمساعدين المعزولين
"""

import os
import sys
import json
from pathlib import Path

# إضافة المسارات للاستيراد
sys.path.append(str(Path(__file__).parent))

def print_banner():
    """طباعة البانر"""
    print("🤖 Augment Assistants - مساعدين Augment Agent")
    print("=" * 50)
    print("نظام مساعدين معزول ومتخصص")
    print("")

def check_system_status():
    """فحص حالة النظام"""
    print("🔍 فحص حالة النظام...")
    print("-" * 30)
    
    status = {}
    
    # فحص الملفات الأساسية
    essential_files = [
        "shared-memory/project-knowledge.json",
        "gemini-interface/gemini-connector.py",
        "README.md"
    ]
    
    files_ok = 0
    for file_path in essential_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
            files_ok += 1
        else:
            print(f"❌ {file_path}")
    
    status["files"] = f"{files_ok}/{len(essential_files)}"
    
    # فحص المجلدات
    directories = [
        "shared-memory", "gemini-interface", "agents-interface", 
        "scripts", "logs"
    ]
    
    dirs_ok = 0
    for dir_path in directories:
        if Path(dir_path).exists():
            dirs_ok += 1
    
    status["directories"] = f"{dirs_ok}/{len(directories)}"
    
    # فحص Gemini CLI
    try:
        from gemini_interface.gemini_connector import test_gemini
        gemini_status = test_gemini()
        status["gemini_cli"] = "متاح" if gemini_status else "غير متاح"
    except Exception as e:
        status["gemini_cli"] = f"خطأ: {str(e)[:50]}"
    
    print(f"\n📊 ملخص الحالة:")
    for component, state in status.items():
        print(f"  {component}: {state}")
    
    return status

def test_gemini_assistant():
    """اختبار مساعد Gemini"""
    print("\n🧠 اختبار مساعد Gemini...")
    print("-" * 30)
    
    try:
        from gemini_interface.gemini_connector import quick_consult
        
        test_query = "ما أفضل طريقة لتنظيم مشروع Python؟"
        print(f"📝 السؤال: {test_query}")
        
        response = quick_consult(test_query)
        print(f"💬 الاستجابة: {response[:200]}...")
        
        return True
    except Exception as e:
        print(f"❌ فشل الاختبار: {str(e)}")
        return False

def show_memory_status():
    """عرض حالة الذاكرة"""
    print("\n🧠 حالة الذاكرة المشتركة...")
    print("-" * 30)
    
    try:
        memory_file = Path("shared-memory/project-knowledge.json")
        if memory_file.exists():
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory = json.load(f)
            
            print(f"📁 المشروع: {memory.get('system_info', {}).get('name', 'غير محدد')}")
            print(f"📅 تاريخ الإنشاء: {memory.get('system_info', {}).get('created', 'غير محدد')}")
            print(f"🛡️ حالة العزل: {memory.get('system_info', {}).get('isolation_status', 'غير محدد')}")
            
            # عرض حالة المساعدين
            assistants = memory.get('assistants', {})
            if 'gemini_cli' in assistants:
                gemini_stats = assistants['gemini_cli'].get('usage_stats', {})
                total = gemini_stats.get('total_consultations', 0)
                successful = gemini_stats.get('successful_responses', 0)
                print(f"🧠 Gemini CLI: {successful}/{total} استشارة ناجحة")
            
            if 'ai_agents' in assistants:
                agents_count = len(assistants['ai_agents'])
                print(f"🤖 AI Agents: {agents_count} وكيل متاح")
        else:
            print("❌ ملف الذاكرة غير موجود")
    except Exception as e:
        print(f"❌ خطأ في قراءة الذاكرة: {str(e)}")

def interactive_menu():
    """قائمة تفاعلية"""
    while True:
        print("\n🎯 الخيارات المتاحة:")
        print("1. 🧠 استشارة Gemini CLI")
        print("2. 📊 عرض حالة النظام")
        print("3. 🧠 عرض حالة الذاكرة")
        print("4. 🔍 اختبار المساعدين")
        print("5. 📝 عرض تاريخ الاستشارات")
        print("0. 🚪 خروج")
        
        choice = input("\n🎯 اختر رقم الخيار: ").strip()
        
        if choice == "1":
            query = input("🤔 أدخل سؤالك لـ Gemini: ").strip()
            if query:
                try:
                    from gemini_interface.gemini_connector import quick_consult
                    response = quick_consult(query)
                    print(f"\n💬 الاستجابة:\n{response}")
                except Exception as e:
                    print(f"❌ خطأ: {str(e)}")
        
        elif choice == "2":
            check_system_status()
        
        elif choice == "3":
            show_memory_status()
        
        elif choice == "4":
            print("\n🧪 اختبار المساعدين...")
            gemini_ok = test_gemini_assistant()
            print(f"\n📊 النتيجة: Gemini CLI {'✅ يعمل' if gemini_ok else '❌ لا يعمل'}")
        
        elif choice == "5":
            try:
                from gemini_interface.gemini_connector import GeminiConnector
                connector = GeminiConnector()
                history = connector.get_consultation_history(5)
                
                print("\n📝 آخر 5 استشارات:")
                for i, consultation in enumerate(history, 1):
                    timestamp = consultation["timestamp"][:16]
                    query = consultation["query"][:50] + "..."
                    success = "✅" if consultation["success"] else "❌"
                    print(f"  {i}. {timestamp} {success} {query}")
            except Exception as e:
                print(f"❌ خطأ في قراءة التاريخ: {str(e)}")
        
        elif choice == "0":
            print("\n👋 شكراً لاستخدام مساعدين Augment!")
            break
        
        else:
            print("❌ اختيار غير صحيح")

def main():
    """الدالة الرئيسية"""
    # تغيير المجلد إلى مجلد المساعدين
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print_banner()
    
    # فحص أولي للنظام
    status = check_system_status()
    
    # تحديد ما إذا كان النظام جاهز
    files_ready = status["files"].split("/")
    files_ok = int(files_ready[0]) >= int(files_ready[1]) * 0.8  # 80% من الملفات موجودة
    
    if files_ok:
        print("\n✅ النظام جاهز للاستخدام!")
        
        # عرض الذاكرة
        show_memory_status()
        
        # بدء القائمة التفاعلية
        interactive_menu()
    else:
        print("\n⚠️ النظام غير مكتمل")
        print("🔧 تأكد من وجود جميع الملفات المطلوبة")

if __name__ == "__main__":
    main()
