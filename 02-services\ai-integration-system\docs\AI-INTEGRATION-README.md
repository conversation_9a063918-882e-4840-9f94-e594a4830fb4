# 🤖 AI Integration System - نظام التكامل الذكي

## 📋 نظرة عامة

نظام تكامل شامل يجمع بين **Gemini CLI** و**الوكلاء الذكيين** و**إضافات VS Code** لإنشاء بيئة تطوير ذكية متكاملة.

## 🏗️ هيكل النظام (Hub and Spoke Architecture)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gemini CLI    │    │   AI Agents     │    │   VS Code       │
│   (Quick Query) │    │   (Execution)   │    │   (Interface)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────┬───────────┴──────────┬───────────┘
                     │                      │
              ┌──────▼──────┐        ┌──────▼──────┐
              │     n8n     │        │ AnythingLLM │
              │ (Coordinator)│        │  (Memory)   │
              └─────────────┘        └─────────────┘
                     │
              ┌──────▼──────┐
              │   Ollama    │
              │  (Models)   │
              └─────────────┘
```

## 🔧 المكونات الرئيسية

### 1. **Gemini CLI** 🧠
- **المسار**: `C:/Users/<USER>/gemini-cli`
- **الغرض**: استعلامات سريعة وتخطيط استراتيجي
- **مفاتيح API**: 4 مفاتيح متاحة للتناوب
- **الاستخدام**: `gemini "سؤالك هنا"`

### 2. **AI Agents** 🤖
- **المسار**: `./ai-agents/`
- **الوكلاء المتاحة**:
  - `memory-agent.py` - إدارة الذاكرة والسياق
  - `file-search-agent.py` - البحث وتحليل الملفات
  - `terminal-agent.py` - عمليات الطرفية
  - `data-analysis-agent.py` - تحليل البيانات
  - `agent-coordinator.py` - تنسيق الوكلاء

### 3. **Ollama Models** 🧠
- **الرابط**: `http://localhost:11434`
- **النماذج المتخصصة**:
  - `llama3:8b` - الذاكرة والبحث
  - `codellama:7b` - عمليات الطرفية
  - `mistral:7b` - تحليل البيانات
  - `gemma3n:e4b` - مهام عامة (7.5 GB)

### 4. **AnythingLLM** 📚
- **الرابط**: `http://localhost:4001`
- **الغرض**: مركز الذاكرة والسياق
- **المشكلة الحالية**: `No embedding base path was set`

### 5. **n8n** ⚙️
- **الرابط**: `http://localhost:5678`
- **الغرض**: منسق العمليات والتدفقات
- **الحالة**: يعمل حالياً

## 🚀 طرق الاستخدام

### 1. **من خلال VS Code Tasks** (الأسهل)
```
Ctrl + Shift + P → Tasks: Run Task
```
- 🤖 طلب بسيط من AI
- 🔍 فحص حالة النظام
- 🤖 AI: تحليل الملف الحالي
- 🚀 AI: توليد كود جديد
- ⚡ Gemini CLI: استعلام سريع

### 2. **من خلال PowerShell Manager**
```powershell
.\ai-integration-manager.ps1
```
- قائمة تفاعلية شاملة
- فحص حالة النظام
- تشغيل الوكلاء
- اختبار التكامل

### 3. **من خلال Python Controller**
```bash
python ai_integration_controller.py --status
python ai_integration_controller.py --query "سؤالك"
python ai_integration_controller.py --agent memory
python ai_integration_controller.py --report
```

### 4. **الأوامر المباشرة**
```bash
# Gemini CLI
gemini "كيف أحسن هذا الكود؟"

# تشغيل وكيل
powershell ai-agents/run-agents.ps1 memory

# فحص نماذج Ollama
ollama list
```

## 📁 الملفات المهمة

### ملفات التكوين
- `ai-integration-system.json` - إعدادات النظام الشاملة
- `.env.integration` - متغيرات البيئة
- `.vscode/settings.json` - إعدادات VS Code المحسنة
- `.vscode/tasks.json` - مهام VS Code المتكاملة

### ملفات التحكم
- `ai_integration_controller.py` - منسق Python
- `ai-integration-manager.ps1` - مدير PowerShell
- `ai-agents/run-agents.ps1` - مشغل الوكلاء

## 🔧 المنافذ المستخدمة

### الحالية
- AnythingLLM: `4001`
- n8n: `5678`
- Ollama: `11434`
- Frontend: `3000`

### المقترحة (موحدة)
- Frontend: `3000`
- AnythingLLM: `3001`
- n8n: `3002`
- Ollama: `3003`
- API Gateway: `3004`

## 🛠️ إصلاح المشاكل الشائعة

### 1. **AnythingLLM لا يستجيب**
```bash
# المشكلة: No embedding base path was set
# الحل: تكوين مسار التضمين في إعدادات AnythingLLM
```

### 2. **استهلاك عالي للموارد**
```bash
# فحص الاستخدام
python ai_integration_controller.py --optimize

# إيقاف النماذج غير المستخدمة
ollama stop model_name
```

### 3. **تعارض المنافذ**
```bash
# فحص المنافذ المستخدمة
netstat -an | findstr :4001
netstat -an | findstr :5678
```

## 📊 مراقبة النظام

### فحص الحالة
```bash
# فحص شامل
python ai_integration_controller.py --status

# تقرير مفصل
python ai_integration_controller.py --report

# من PowerShell
.\ai-integration-manager.ps1 status
```

### مراقبة الموارد
- استخدام الذاكرة: يجب أن يكون أقل من 80%
- النماذج النشطة: يُفضل 3 نماذج أو أقل
- المنافذ: تجنب التعارضات

## 🔄 تدفقات العمل

### 1. **استعلام بسيط**
```
User → VS Code Task → Gemini CLI → Result
```

### 2. **تحليل معقد**
```
User → n8n → File Agent → AnythingLLM → Gemini API → Ollama → VS Code
```

### 3. **تطوير كود**
```
User → VS Code → GitHub Copilot + Gemini CLI → Code Generation
```

## 📝 الذاكرة والجلسات

### مجلدات الذاكرة
- `./memory/sessions/` - جلسات العمل
- `./memory/projects/` - وثائق المشاريع
- `./memory/agents/` - سجلات الوكلاء
- `./memory/integration/` - سجلات التكامل

### إدارة الجلسات
- حفظ تلقائي كل ساعة
- تنظيف الجلسات القديمة (30 يوم)
- تشفير البيانات الحساسة

## 🚀 الخطوات التالية

### فورية
1. ✅ إصلاح مشكلة AnythingLLM embedding
2. ✅ توحيد نظام المنافذ
3. ✅ تحسين إدارة الموارد

### متوسطة المدى
1. تطوير إضافة VS Code مخصصة
2. إنشاء قوالب تدفق عمل تلقائية
3. تطبيق نظام إدارة الجلسات

### طويلة المدى
1. نشر في بيئة الإنتاج
2. إنشاء وثائق شاملة
3. تطبيق مراقبة وتحليلات

## 🆘 الدعم والمساعدة

### الأوامر السريعة
```bash
# مساعدة Python Controller
python ai_integration_controller.py --help

# مساعدة PowerShell Manager
.\ai-integration-manager.ps1 -Help

# مساعدة Gemini CLI
gemini --help
```

### الموارد
- ملف التكوين: `ai-integration-system.json`
- سجلات النظام: `./logs/ai-integration.log`
- وثائق الوكلاء: `./ai-agents/README.md`

---

**تم إنشاؤه بواسطة**: Amr Ashour  
**التاريخ**: 2025-01-06  
**الإصدار**: 1.0.0
