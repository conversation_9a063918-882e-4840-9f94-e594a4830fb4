# دليل الأوامر السريعة - MCP & AnythingLLM
# =============================================

## 📅 تاريخ الإنشاء: 2025-07-05

## 🚀 أوامر MCP Servers السريعة:

### التنقل إلى مجلد MCP:
```powershell
cd C:\Users\<USER>\mcp-servers
```

### تشغيل جميع الخوادم:
```powershell
.\manage-mcp.ps1 -Action start
```

### تشغيل خادم محدد:
```powershell
.\manage-mcp.ps1 -Action start -Service github-mcp
.\manage-mcp.ps1 -Action start -Service filesystem-mcp
.\manage-mcp.ps1 -Action start -Service duckduckgo-mcp
```

### عرض حالة الخوادم:
```powershell
.\manage-mcp.ps1 -Action status
```

### عرض السجلات:
```powershell
# جميع الخوادم
.\manage-mcp.ps1 -Action logs

# خادم محدد
.\manage-mcp.ps1 -Action logs -Service github-mcp
```

### إيقاف الخوادم:
```powershell
# جميع الخوادم
.\manage-mcp.ps1 -Action stop

# خادم محدد
.\manage-mcp.ps1 -Action stop -Service github-mcp
```

### إعادة تشغيل الخوادم:
```powershell
.\manage-mcp.ps1 -Action restart
```

### تحديث الخوادم:
```powershell
.\manage-mcp.ps1 -Action update
```

## 🐳 أوامر Docker المفيدة:

### عرض الحاويات المشغلة:
```bash
docker ps
docker ps --filter "name=mcp-"
```

### عرض جميع الحاويات:
```bash
docker ps -a
```

### فحص استخدام الموارد:
```bash
docker stats
docker stats --no-stream
```

### عرض الشبكات:
```bash
docker network ls
docker network inspect mcp-network
```

### عرض الـ volumes:
```bash
docker volume ls
```

### تنظيف النظام:
```bash
# تنظيف شامل
docker system prune -f

# تنظيف الصور غير المستخدمة
docker image prune -f

# تنظيف الـ volumes غير المستخدمة
docker volume prune -f
```

## 🌐 فحص صحة الخوادم:

### فحص المنافذ:
```bash
# فحص منفذ محدد
netstat -an | findstr :8801

# فحص جميع منافذ MCP
netstat -an | findstr "880"
```

### فحص صحة الخوادم:
```bash
# GitHub MCP
curl http://localhost:8801/health

# Filesystem MCP
curl http://localhost:8802/health

# DuckDuckGo MCP
curl http://localhost:8803/health

# Docker MCP (من Docker Desktop)
curl http://localhost:8811/health
```

## 📁 إدارة الملفات:

### التنقل بين المجلدات:
```powershell
# مجلد AnythingLLM
cd "C:\Users\<USER>\anything llm"

# مجلد MCP Servers
cd "C:\Users\<USER>\mcp-servers"

# مجلد Memory
cd "C:\Users\<USER>\anything llm\memory"
```

### عرض محتويات المجلدات:
```powershell
# عرض الملفات
ls
dir

# عرض تفصيلي
ls -la
dir /a
```

### تحرير الملفات:
```powershell
# تحرير ملف البيئة
notepad .env

# تحرير Docker Compose
notepad docker-compose.yml

# تحرير سكريبت الإدارة
notepad manage-mcp.ps1
```

## 🔧 أوامر AnythingLLM:

### التنقل إلى مجلد AnythingLLM:
```powershell
cd "C:\Users\<USER>\anything llm"
```

### تشغيل AnythingLLM:
```bash
# إذا كان مثبت بـ Docker
docker-compose up -d

# إذا كان مثبت محلياً
npm start
```

### فحص حالة AnythingLLM:
```bash
# فحص الحاويات
docker ps | findstr anything

# فحص المنفذ
netstat -an | findstr :3001
```

### الوصول إلى AnythingLLM:
```
URL: http://localhost:3001
```

## 🔍 أوامر التشخيص:

### فحص حالة Docker:
```bash
docker version
docker system info
docker system df
```

### فحص استخدام المنافذ:
```bash
# Windows
netstat -ano | findstr :8801
netstat -ano | findstr :3001

# عرض العمليات المستخدمة للمنافذ
netstat -ano | findstr LISTENING
```

### فحص العمليات:
```bash
# عرض العمليات المرتبطة بـ Docker
tasklist | findstr docker

# عرض استخدام الذاكرة
tasklist /fo table | findstr docker
```

## 📊 مراقبة الأداء:

### مراقبة Docker:
```bash
# استخدام الموارد
docker stats --no-stream

# معلومات النظام
docker system df

# عرض الأحداث
docker events --since 1h
```

### مراقبة النظام:
```powershell
# استخدام CPU والذاكرة
Get-Process | Sort-Object CPU -Descending | Select-Object -First 10

# مساحة القرص
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace
```

## 🔄 أوامر الصيانة:

### نسخ احتياطي:
```powershell
# نسخ ملفات الإعداد
Copy-Item .env .env.backup
Copy-Item docker-compose.yml docker-compose.yml.backup

# نسخ مجلد كامل
Copy-Item -Recurse configs configs-backup
```

### استعادة النسخ الاحتياطية:
```powershell
Copy-Item .env.backup .env
Copy-Item docker-compose.yml.backup docker-compose.yml
```

### تحديث النظام:
```bash
# تحديث صور Docker
docker-compose pull

# إعادة إنشاء الحاويات
docker-compose up -d --force-recreate
```

## 🚨 أوامر الطوارئ:

### إيقاف فوري لجميع الحاويات:
```bash
docker stop $(docker ps -q)
```

### حذف جميع الحاويات المتوقفة:
```bash
docker container prune -f
```

### إعادة تشغيل Docker Desktop:
```powershell
# إيقاف
Stop-Service com.docker.service

# تشغيل
Start-Service com.docker.service
```

### إعادة تعيين شبكة Docker:
```bash
docker network prune -f
docker network create mcp-network
```

## 📝 ملاحظات سريعة:

### متغيرات البيئة المهمة:
```bash
GITHUB_TOKEN=ghp_xxxxxxxxxxxx
AZURE_CLIENT_ID=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
POSTGRES_USER=your_username
POSTGRES_PASSWORD=your_password
```

### منافذ مهمة:
- AnythingLLM: 3001
- GitHub MCP: 8801
- Filesystem MCP: 8802
- DuckDuckGo MCP: 8803
- Docker MCP: 8811

### مجلدات مهمة:
- AnythingLLM: `C:\Users\<USER>\anything llm`
- MCP Servers: `C:\Users\<USER>\mcp-servers`
- Memory: `C:\Users\<USER>\anything llm\memory`

## 🤖 أوامر Gemini CLI:

### تشغيل Gemini CLI:
```powershell
# تشغيل Gemini
gemini
```

### أوامر داخل Gemini:
```bash
# المساعدة
/help

# التوثيق
/docs

# الخروج
/quit

# قراءة ملف محدد
@path/to/file

# مثال: قراءة ملف MCP
@mcp-servers/README.md
```

### إعداد المصادقة:
```powershell
# تعيين API Key للجلسة الحالية
$env:GOOGLE_API_KEY = "your-api-key-here"

# تعيين API Key بشكل دائم
[Environment]::SetEnvironmentVariable("GOOGLE_API_KEY", "your-api-key-here", "User")

# Google Cloud SDK (إذا كان مثبت)
gcloud auth login
gcloud auth application-default login
```

### فحص حالة Gemini:
```powershell
# فحص إذا كان Gemini مثبت
where gemini
Get-Command gemini

# فحص متغيرات البيئة
$env:GOOGLE_API_KEY
$env:GOOGLE_APPLICATION_CREDENTIALS
```

## 🎯 نصائح للاستخدام اليومي:

1. **فحص الحالة أولاً**: دائماً ابدأ بـ `.\manage-mcp.ps1 -Action status`
2. **مراقبة السجلات**: استخدم `.\manage-mcp.ps1 -Action logs` عند وجود مشاكل
3. **تنظيف دوري**: شغل `docker system prune -f` أسبوعياً
4. **نسخ احتياطي**: احفظ نسخة من ملف `.env` بانتظام
5. **مراقبة الأداء**: استخدم `docker stats` لمراقبة استخدام الموارد
6. **استخدم Gemini CLI**: للمساعدة في فهم وتطوير المشروع
