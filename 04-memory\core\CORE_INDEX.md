# 📁 فهرس المحتوى الرئيسي

تم تنظيم جميع ملفات المشروع في مجلدات مخصصة لسهولة الإدارة والوصول.

## 📂 هيكل المجلدات

### 📚 `docs/` - التوثيق والأدلة
- `QUICK_SETUP_GUIDE.md` - دليل الإعداد السريع
- `system_integration_guide.md` - دليل تكامل النظام
- `google_cloud_setup.md` - إعداد Google Cloud
- `DOCKER_IMAGES_GUIDE.md` - دليل صور Docker
- `n8n_workflow_guide.md` - دليل سير عمل n8n
- `README_AI_WORKFLOW.md` - دليل سير العمل الذكي
- `integrated_ai_system_plan.md` - خطة النظام المتكامل
- `full_system_analysis.md` - تحليل النظام الكامل
- `DEPLOYMENT_SUCCESS.md` - تأكيد نجاح النشر
- `import_workflow_guide.md` - دليل استيراد سير العمل
- `PROJECT_README.md` - ملف تعريف المشروع
- `ANYTHING_LLM_ANALYSIS.md` - تحليل AnythingLLM
- `conversation-session-2025-07-05.md` - جلسة المحادثة

### 🐍 `scripts/` - السكريبتات Python
- `vscode_ai_controller.py` - المتحكم الذكي الرئيسي
- `install_dependencies.py` - مثبت المتطلبات
- `test_system.py` - اختبار النظام
- `test_ai_workflow.py` - اختبار سير العمل الذكي
- `test_webhook.py` - اختبار الـ webhook
- `upload_workflow.py` - رفع سير العمل

### ⚙️ `configs/` - ملفات التكوين
- `docker-compose.yml` - تكوين Docker الكامل
- `docker-compose-simple.yml` - تكوين Docker المبسط
- `requirements.txt` - متطلبات Python
- `.gitignore` - ملفات Git المستبعدة

### 🔄 `workflows/` - سير العمل والأتمتة
- `ai_dev_assistant_workflow.json` - سير عمل مساعد المطور
- `quick_start.sh` - سكريبت البدء السريع

### 📊 `dashboard/` - لوحة التحكم
- `ai_dashboard/` - تطبيق لوحة التحكم الكامل
  - `app.py` - التطبيق الرئيسي
  - `styles.css` - الأنماط
  - `template.html` - القالب الرئيسي
  - `static/` - الملفات الثابتة
  - `templates/` - القوالب

## 🎯 كيفية الاستخدام

### البدء السريع:
```bash
# الانتقال إلى مجلد التكوينات
cd core/configs

# تشغيل النظام
docker-compose -f docker-compose-simple.yml up -d

# الانتقال إلى مجلد السكريبتات
cd ../scripts

# تثبيت المتطلبات
python install_dependencies.py

# تشغيل المتحكم الذكي
python vscode_ai_controller.py --check-status
```

### الوصول للتوثيق:
```bash
# فتح دليل الإعداد السريع
cd core/docs
# قراءة QUICK_SETUP_GUIDE.md
```

### استيراد سير العمل:
```bash
cd core/workflows
# استخدام ai_dev_assistant_workflow.json في n8n
```

## 🔗 الروابط المهمة

- **AnythingLLM**: `http://localhost:3001`
- **n8n**: `http://localhost:5678`
- **Ollama**: `http://localhost:11434`
- **AI Dashboard**: `python core/dashboard/ai_dashboard/app.py`

## 📋 الملفات المتبقية في الجذر

- `README.md` - الملف الرئيسي للمشروع
- `start.text` - ملف بداية المشروع
- `chat-gemini-pro.text` - محادثة Gemini
- `anything-llm/` - مشروع AnythingLLM الكامل
- `memory/` - ذاكرة النظام والإعدادات
- `.vscode/` - إعدادات VS Code
- `.venv/` - البيئة الافتراضية Python

---
*تم إنشاؤه في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}*
