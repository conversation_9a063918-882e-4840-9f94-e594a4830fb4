#!/usr/bin/env python3
"""
سكريبت اختبار AI Dev Assistant Workflow
"""

import requests
import json

def test_workflow():
    """اختبار الـ workflow"""
    
    webhook_url = "http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22"
    
    # بيانات الاختبار
    test_data = {
        "feature_request": "نظام تسجيل دخول للمستخدمين مع التحقق من الهوية باستخدام JWT وحفظ بيانات المستخدم في قاعدة بيانات",
        "feature_name": "user_authentication_system"
    }
    
    print("🚀 اختبار AI Dev Assistant Workflow")
    print("=" * 50)
    print(f"📝 الميزة المطلوبة: {test_data['feature_request']}")
    print(f"📁 اسم الملف: {test_data['feature_name']}")
    print("\n⏳ إرسال الطلب...")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=180  # 3 دقائق
        )
        
        if response.status_code == 200:
            print("✅ تم إرسال الطلب بنجاح!")
            print(f"📊 الاستجابة: {response.text}")
            
            # محاولة عرض الملف المُنشأ
            print("\n📄 محاولة عرض الملف المُنشأ...")
            import subprocess
            try:
                result = subprocess.run([
                    "docker", "exec", "n8n_automation", 
                    "cat", f"/data/output/{test_data['feature_name']}.md"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print("📋 محتوى الملف:")
                    print("-" * 50)
                    print(result.stdout)
                else:
                    print("❌ لم يتم العثور على الملف بعد")
            except Exception as e:
                print(f"⚠️ خطأ في قراءة الملف: {e}")
                
        else:
            print(f"❌ فشل في إرسال الطلب: {response.status_code}")
            print(f"📊 الاستجابة: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ انتهت المهلة الزمنية - قد يكون الـ workflow لا يزال يعمل")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_workflow()
