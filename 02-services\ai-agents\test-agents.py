#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Test AI Agents - AI Development Assistant
============================================
اختبار سريع لجميع الوكلاء
"""

import sys
import time
import requests

def test_ollama_connection():
    """اختبار الاتصال مع Ollama"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama متصل - النماذج المتاحة: {len(models)}")
            for model in models:
                print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Ollama غير متاح - كود الحالة: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال مع Ollama: {e}")
        return False

def test_simple_generation():
    """اختبار توليد نص بسيط"""
    try:
        data = {
            "model": "phi3:mini",
            "prompt": "مرحبا، كيف حالك؟",
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json().get("response", "")
            print(f"✅ اختبار التوليد نجح: {result[:50]}...")
            return True
        else:
            print(f"❌ فشل اختبار التوليد: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التوليد: {e}")
        return False

def test_memory_agent():
    """اختبار وكيل الذاكرة"""
    try:
        from memory_agent import MemoryAgent
        agent = MemoryAgent()
        
        # اختبار بسيط
        report = agent.generate_session_report()
        print(f"✅ Memory Agent يعمل - الجلسات: {report.get('total_sessions', 0)}")
        return True
        
    except Exception as e:
        print(f"❌ Memory Agent فشل: {e}")
        return False

def test_file_search_agent():
    """اختبار وكيل البحث في الملفات"""
    try:
        from file_search_agent import FileSearchAgent
        agent = FileSearchAgent()
        
        # مسح المجلد الحالي
        files = agent.scan_directory(".", max_files=5)
        print(f"✅ File Search Agent يعمل - الملفات: {len(files)}")
        return True
        
    except Exception as e:
        print(f"❌ File Search Agent فشل: {e}")
        return False

def test_terminal_agent():
    """اختبار وكيل الترمينال"""
    try:
        from terminal_agent import TerminalAgent
        agent = TerminalAgent()
        
        # اختبار معلومات النظام
        info = agent.get_system_info()
        if "error" not in info:
            print(f"✅ Terminal Agent يعمل - النظام: {info['os']['system']}")
            return True
        else:
            print(f"❌ Terminal Agent خطأ: {info['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Terminal Agent فشل: {e}")
        return False

def test_data_analysis_agent():
    """اختبار وكيل تحليل البيانات"""
    try:
        from data_analysis_agent import DataAnalysisAgent
        agent = DataAnalysisAgent()
        
        # إنشاء بيانات تجريبية
        import pandas as pd
        import numpy as np
        
        data = pd.DataFrame({
            'x': np.random.randn(10),
            'y': np.random.randn(10)
        })
        
        stats = agent.basic_statistics(data)
        if "error" not in stats:
            print(f"✅ Data Analysis Agent يعمل - الأعمدة: {len(data.columns)}")
            return True
        else:
            print(f"❌ Data Analysis Agent خطأ: {stats['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Data Analysis Agent فشل: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🧪 اختبار الوكلاء الذكيين")
    print("=" * 40)
    
    tests = [
        ("اتصال Ollama", test_ollama_connection),
        ("توليد النص", test_simple_generation),
        ("وكيل الذاكرة", test_memory_agent),
        ("وكيل البحث", test_file_search_agent),
        ("وكيل الترمينال", test_terminal_agent),
        ("وكيل تحليل البيانات", test_data_analysis_agent)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "=" * 40)
    print("📊 نتائج الاختبارات:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع الوكلاء تعمل بنجاح!")
    elif passed > len(results) // 2:
        print("⚠️ معظم الوكلاء تعمل، راجع الأخطاء")
    else:
        print("🚨 مشاكل كبيرة، راجع إعداد Ollama")

if __name__ == "__main__":
    main()
