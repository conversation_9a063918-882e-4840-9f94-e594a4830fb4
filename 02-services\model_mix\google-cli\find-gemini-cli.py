#!/usr/bin/env python3
"""
البحث عن مسار gemini-cli المحلي وتحديث docker-compose.yml
Find local gemini-cli path and update docker-compose.yml
"""

import os
import sys
import subprocess
from pathlib import Path
import yaml

def find_gemini_cli_paths():
    """البحث عن مسارات gemini-cli المحتملة"""
    possible_paths = []
    
    # مسارات شائعة
    common_locations = [
        Path.home() / "gemini-cli",
        Path.home() / "Documents" / "gemini-cli",
        Path.home() / "Projects" / "gemini-cli",
        Path.home() / "dev" / "gemini-cli",
        Path.home() / "code" / "gemini-cli",
        Path("C:/") / "gemini-cli",
        Path("C:/Users") / os.getenv('USERNAME', '') / "gemini-cli",
    ]
    
    # البحث في المسارات الشائعة
    for path in common_locations:
        if path.exists() and (path / "package.json").exists():
            possible_paths.append(str(path))
    
    # البحث باستخدام git
    try:
        result = subprocess.run([
            'git', 'config', '--global', '--get-regexp', 'remote.*url'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'gemini-cli' in line:
                    # استخراج المسار من إعدادات git
                    pass
    except:
        pass
    
    # البحث في مجلدات المستخدم
    user_dirs = [
        Path.home(),
        Path.home() / "Documents",
        Path.home() / "Desktop"
    ]
    
    for user_dir in user_dirs:
        if user_dir.exists():
            try:
                for item in user_dir.iterdir():
                    if item.is_dir() and 'gemini' in item.name.lower():
                        if (item / "package.json").exists():
                            possible_paths.append(str(item))
            except PermissionError:
                continue
    
    return list(set(possible_paths))  # إزالة المكررات

def verify_gemini_cli(path):
    """التحقق من أن المسار يحتوي على gemini-cli صحيح"""
    path = Path(path)
    
    # التحقق من وجود package.json
    package_json = path / "package.json"
    if not package_json.exists():
        return False, "package.json not found"
    
    try:
        import json
        with open(package_json, 'r') as f:
            package_data = json.load(f)
        
        # التحقق من اسم المشروع
        if 'gemini' not in package_data.get('name', '').lower():
            return False, "Not a gemini project"
        
        return True, "Valid gemini-cli project"
        
    except Exception as e:
        return False, f"Error reading package.json: {e}"

def update_docker_compose(gemini_cli_path):
    """تحديث docker-compose.yml بالمسار الصحيح"""
    compose_file = Path("docker-compose.yml")
    
    if not compose_file.exists():
        print("❌ docker-compose.yml not found")
        return False
    
    try:
        # قراءة الملف الحالي
        with open(compose_file, 'r') as f:
            content = f.read()
        
        # تحديث المسار
        # البحث عن السطر الذي يحتوي على المسار القديم
        lines = content.split('\n')
        updated_lines = []
        
        for line in lines:
            if 'C:/path/to/your/gemini-cli' in line:
                # استبدال المسار
                indent = len(line) - len(line.lstrip())
                new_line = ' ' * indent + f"- {gemini_cli_path}:/app/gemini-cli-local"
                updated_lines.append(new_line)
                print(f"✅ تم تحديث المسار إلى: {gemini_cli_path}")
            else:
                updated_lines.append(line)
        
        # كتابة الملف المحدث
        with open(compose_file, 'w') as f:
            f.write('\n'.join(updated_lines))
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث docker-compose.yml: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 البحث عن gemini-cli المحلي...")
    print("=" * 50)
    
    # البحث عن المسارات
    paths = find_gemini_cli_paths()
    
    if not paths:
        print("❌ لم يتم العثور على gemini-cli")
        print("\n💡 تأكد من:")
        print("1. تثبيت gemini-cli محلياً")
        print("2. وجود ملف package.json في مجلد gemini-cli")
        print("3. أن المجلد في أحد المواقع الشائعة")
        return
    
    print(f"✅ تم العثور على {len(paths)} مسار محتمل:")
    
    valid_paths = []
    for i, path in enumerate(paths, 1):
        is_valid, message = verify_gemini_cli(path)
        status = "✅" if is_valid else "❌"
        print(f"{i}. {status} {path}")
        print(f"   {message}")
        
        if is_valid:
            valid_paths.append(path)
    
    if not valid_paths:
        print("\n❌ لم يتم العثور على gemini-cli صحيح")
        return
    
    # اختيار المسار
    if len(valid_paths) == 1:
        selected_path = valid_paths[0]
        print(f"\n📍 تم اختيار: {selected_path}")
    else:
        print(f"\n📋 المسارات الصحيحة:")
        for i, path in enumerate(valid_paths, 1):
            print(f"{i}. {path}")
        
        try:
            choice = int(input("\nاختر رقم المسار: ")) - 1
            if 0 <= choice < len(valid_paths):
                selected_path = valid_paths[choice]
            else:
                print("❌ اختيار غير صحيح")
                return
        except ValueError:
            print("❌ يرجى إدخال رقم صحيح")
            return
    
    # تحديث docker-compose.yml
    if update_docker_compose(selected_path):
        print("\n🎉 تم التحديث بنجاح!")
        print("📋 الخطوات التالية:")
        print("1. تأكد من تعيين GEMINI_API_KEY في .env")
        print("2. شغل: docker-compose up -d")
        print("3. ادخل إلى الحاوية: docker exec -it gemini-cli bash")
    else:
        print("\n❌ فشل في التحديث")

if __name__ == "__main__":
    main()
