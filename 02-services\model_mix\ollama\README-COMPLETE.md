# 🤖 Ollama Docker Setup - دليل شامل

## 📋 نظرة عامة

هذا المشروع يوفر إعداد Docker كامل لتشغيل Ollama مع واجهة ويب حديثة، مع إمكانية نسخ النماذج من التثبيت المحلي.

### المكونات:
- **🧠 Ollama**: خادم النماذج اللغوية الكبيرة
- **🌐 Open WebUI**: واجهة ويب تفاعلية وحديثة
- **🐳 Docker Compose**: لإدارة الحاويات

## 🚀 التشغيل السريع

### 1. تشغيل النظام
```bash
cd ollama
docker-compose up -d
```

### 2. الوصول إلى الخدمات
- **🌐 واجهة الويب**: http://localhost:3000
- **🔌 Ollama API**: http://localhost:11434

## 📦 النماذج المتاحة

### النماذج المحلية (من C:\ollama_models):
- **🦙 Llama3:8b** (4.7 GB) - نموذج قوي للمحادثات
- **🔬 Phi3:mini** (2.2 GB) - نموذج صغير وسريع

### نسخ النماذج إلى الحاوية:
```bash
# نسخ جميع النماذج
docker cp "C:\ollama_models\." ollama:/root/.ollama/

# إعادة تشغيل الحاوية
docker-compose restart ollama

# التحقق من النماذج
docker exec ollama ollama list
```

## 🛠️ الأوامر المفيدة

### إدارة النماذج:
```bash
# عرض النماذج
docker exec ollama ollama list

# تشغيل نموذج
docker exec -it ollama ollama run phi3:mini

# حذف نموذج
docker exec ollama ollama rm model-name
```

### إدارة الحاويات:
```bash
# عرض الحالة
docker-compose ps

# عرض السجلات
docker-compose logs -f

# إيقاف النظام
docker-compose down

# إعادة تشغيل
docker-compose restart
```

## 🧪 اختبار النظام

### اختبار API:
```powershell
# اختبار الاتصال
Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -UseBasicParsing

# اختبار توليد النص
$body = @{
    model = "phi3:mini"
    prompt = "مرحبا، كيف حالك؟"
    stream = $false
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:11434/api/generate" -Method POST -Body $body -ContentType "application/json"
```

### اختبار واجهة الويب:
1. افتح http://localhost:3000
2. قم بإنشاء حساب جديد
3. اختر نموذج من القائمة
4. ابدأ محادثة

## ⚙️ التكوين

### ملف .env:
```env
OLLAMA_HOST=0.0.0.0:11434
WEBUI_SECRET_KEY=your-secret-key
OLLAMA_DEBUG=1
```

### دعم GPU (NVIDIA):
قم بإلغاء التعليق على قسم GPU في `docker-compose.yml`

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### واجهة الويب لا تعمل:
```bash
docker-compose logs ollama-webui
docker-compose restart ollama-webui
```

#### النماذج لا تظهر:
```bash
# إعادة نسخ النماذج
docker cp "C:\ollama_models\." ollama:/root/.ollama/
docker-compose restart ollama
```

#### Ollama لا يستجيب:
```bash
docker-compose logs ollama
docker-compose restart ollama
```

## 📊 مراقبة الأداء

```bash
# استخدام الموارد
docker stats ollama ollama-webui

# مساحة القرص
docker system df
```

## 🔒 الأمان

⚠️ **للإنتاج**:
1. غير `WEBUI_SECRET_KEY` في `.env`
2. استخدم HTTPS مع reverse proxy
3. قيد الوصول للمنافذ

## 📚 الملفات المهمة

```
ollama/
├── docker-compose.yml    # تكوين الحاويات
├── .env                 # المتغيرات البيئية
├── README.md           # هذا الملف
├── start.sh            # سكريبت التشغيل
└── copy-models.sh      # سكريبت نسخ النماذج
```

---

**✅ النظام جاهز للاستخدام!**

- واجهة الويب: http://localhost:3000
- Ollama API: http://localhost:11434
- النماذج: قيد النسخ من C:\ollama_models
