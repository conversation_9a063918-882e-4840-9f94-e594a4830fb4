#!/usr/bin/env python3
"""
اختبار بسيط لمكونات النظام - جزء بجزء
Simple Component Test - Part by Part
"""

import os
import sys
import subprocess
import requests
from pathlib import Path

def test_component(name, test_func):
    """اختبار مكون واحد"""
    print(f"\n🧪 اختبار {name}...")
    print("=" * 30)
    
    try:
        result = test_func()
        if result:
            print(f"✅ {name}: نجح")
            return True
        else:
            print(f"❌ {name}: فشل")
            return False
    except Exception as e:
        print(f"❌ {name}: خطأ - {str(e)}")
        return False

def test_python():
    """اختبار Python"""
    try:
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True, timeout=5)
        print(f"📝 Python: {result.stdout.strip()}")
        return result.returncode == 0
    except:
        return False

def test_gemini_cli():
    """اختبار Gemini CLI"""
    try:
        # تغيير المجلد
        original_cwd = os.getcwd()
        os.chdir("C:/Users/<USER>")
        
        # اختبار الإصدار
        result = subprocess.run(["gemini", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        os.chdir(original_cwd)
        
        if result.returncode == 0:
            print("📝 Gemini CLI: متاح")
            return True
        else:
            print("📝 Gemini CLI: غير متاح")
            return False
    except Exception as e:
        print(f"📝 Gemini CLI: خطأ - {str(e)}")
        return False

def test_ai_agents():
    """اختبار الوكلاء"""
    try:
        agents_path = Path("../ai-agents")
        if not agents_path.exists():
            agents_path = Path("ai-agents")
        
        if agents_path.exists():
            agent_files = list(agents_path.glob("*-agent.py"))
            print(f"📝 الوكلاء: {len(agent_files)} وكيل متاح")
            for agent in agent_files:
                print(f"   • {agent.name}")
            return len(agent_files) > 0
        else:
            print("📝 مجلد الوكلاء غير موجود")
            return False
    except:
        return False

def test_vscode_config():
    """اختبار إعدادات VS Code"""
    try:
        vscode_path = Path("../.vscode")
        if not vscode_path.exists():
            vscode_path = Path(".vscode")
        
        settings_file = vscode_path / "settings.json"
        tasks_file = vscode_path / "tasks.json"
        
        settings_exists = settings_file.exists()
        tasks_exists = tasks_file.exists()
        
        print(f"📝 VS Code Settings: {'✅' if settings_exists else '❌'}")
        print(f"📝 VS Code Tasks: {'✅' if tasks_exists else '❌'}")
        
        return settings_exists and tasks_exists
    except:
        return False

def test_integration_files():
    """اختبار ملفات التكامل"""
    try:
        base_path = Path(".")
        
        files_to_check = [
            "config/ai-integration-system.json",
            "scripts/ai_integration_controller.py",
            "scripts/ai-integration-manager.ps1",
            "docs/AI-INTEGRATION-README.md"
        ]
        
        found_files = 0
        for file_path in files_to_check:
            full_path = base_path / file_path
            exists = full_path.exists()
            print(f"📝 {file_path}: {'✅' if exists else '❌'}")
            if exists:
                found_files += 1
        
        return found_files >= 3  # على الأقل 3 ملفات موجودة
    except:
        return False

def test_services_offline():
    """اختبار الخدمات (متوقع أن تكون متوقفة)"""
    services = {
        "Ollama": "http://localhost:11434",
        "AnythingLLM": "http://localhost:4001", 
        "n8n": "http://localhost:5678"
    }
    
    offline_count = 0
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=2)
            print(f"📝 {name}: 🟢 يعمل")
        except:
            print(f"📝 {name}: 🔴 متوقف (متوقع)")
            offline_count += 1
    
    # نتوقع أن تكون جميع الخدمات متوقفة
    return offline_count == len(services)

def main():
    """الدالة الرئيسية"""
    print("🤖 اختبار مكونات نظام التكامل الذكي")
    print("=" * 50)
    print("📍 اختبار كل مكون بشكل منفصل...")
    
    # قائمة الاختبارات
    tests = [
        ("Python Environment", test_python),
        ("Gemini CLI", test_gemini_cli),
        ("AI Agents", test_ai_agents),
        ("VS Code Configuration", test_vscode_config),
        ("Integration Files", test_integration_files),
        ("Services Status (Offline)", test_services_offline)
    ]
    
    # تشغيل الاختبارات
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_component(test_name, test_func):
            passed += 1
    
    # النتيجة النهائية
    print(f"\n📊 النتيجة النهائية:")
    print("=" * 30)
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    print(f"📈 النسبة: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.7:  # 70% أو أكثر
        print("\n🎉 النظام جاهز للاستخدام!")
        print("💡 يمكنك الآن تشغيل المكونات حسب الحاجة")
    else:
        print("\n⚠️ النظام يحتاج إلى إصلاحات")
        print("🔧 راجع المكونات التي فشلت")
    
    return passed >= total * 0.7

if __name__ == "__main__":
    # تغيير المجلد إلى مجلد النظام
    script_dir = Path(__file__).parent
    integration_dir = script_dir.parent
    os.chdir(integration_dir)
    
    success = main()
    sys.exit(0 if success else 1)
