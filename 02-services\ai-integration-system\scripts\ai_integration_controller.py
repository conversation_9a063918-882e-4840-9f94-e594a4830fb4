#!/usr/bin/env python3
"""
AI Integration Controller
نظام التحكم المتكامل للذكاء الاصطناعي
"""

import os
import sys
import json
import subprocess
import requests
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse

class AIIntegrationController:
    """منسق النظام المتكامل للذكاء الاصطناعي"""
    
    def __init__(self):
        self.config = self.load_config()
        self.workspace_root = Path.cwd()
        
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات التكامل"""
        config_file = Path("ai-integration-system.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def check_system_status(self) -> Dict[str, str]:
        """فحص حالة جميع مكونات النظام"""
        status = {}
        
        # فحص Ollama
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            status["ollama"] = "✅ متصل" if response.status_code == 200 else "❌ غير متاح"
        except:
            status["ollama"] = "❌ غير متاح"
        
        # فحص AnythingLLM
        try:
            response = requests.get("http://localhost:4001", timeout=5)
            status["anythingllm"] = "✅ متصل" if response.status_code == 200 else "❌ غير متاح"
        except:
            status["anythingllm"] = "❌ غير متاح"
        
        # فحص n8n
        try:
            response = requests.get("http://localhost:5678", timeout=5)
            status["n8n"] = "✅ متصل" if response.status_code == 200 else "❌ غير متاح"
        except:
            status["n8n"] = "❌ غير متاح"
        
        # فحص Gemini CLI
        try:
            result = subprocess.run(["gemini", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            status["gemini_cli"] = "✅ متاح" if result.returncode == 0 else "❌ غير متاح"
        except:
            status["gemini_cli"] = "❌ غير متاح"
        
        # فحص الوكلاء
        agents_path = Path("ai-agents")
        if agents_path.exists():
            agent_files = list(agents_path.glob("*-agent.py"))
            status["agents"] = f"✅ {len(agent_files)} وكيل متاح"
        else:
            status["agents"] = "❌ مجلد الوكلاء غير موجود"
        
        return status
    
    def run_gemini_query(self, query: str) -> str:
        """تشغيل استعلام عبر Gemini CLI"""
        try:
            # تغيير المجلد إلى المجلد الجذر للمستخدم
            original_cwd = os.getcwd()
            os.chdir("C:/Users/<USER>")
            
            result = subprocess.run(
                ["gemini", query],
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8'
            )
            
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                return result.stdout
            else:
                return f"خطأ: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "خطأ: انتهت مهلة الاستعلام"
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def run_agent(self, agent_name: str) -> str:
        """تشغيل وكيل محدد"""
        try:
            agent_file = f"ai-agents/{agent_name}-agent.py"
            if not Path(agent_file).exists():
                return f"❌ الوكيل {agent_name} غير موجود"
            
            result = subprocess.run(
                ["python", agent_file],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                return f"✅ تم تشغيل الوكيل {agent_name} بنجاح\n{result.stdout}"
            else:
                return f"❌ فشل تشغيل الوكيل {agent_name}\n{result.stderr}"
                
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def get_ollama_models(self) -> List[str]:
        """الحصول على قائمة نماذج Ollama المتاحة"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                return [model["name"] for model in data.get("models", [])]
            return []
        except:
            return []
    
    def create_workflow_template(self, workflow_type: str) -> str:
        """إنشاء قالب تدفق عمل"""
        templates = {
            "simple_query": {
                "name": "استعلام بسيط",
                "steps": [
                    "تلقي الاستعلام من المستخدم",
                    "معالجة الاستعلام عبر Gemini CLI",
                    "إرجاع النتيجة"
                ]
            },
            "file_analysis": {
                "name": "تحليل الملفات",
                "steps": [
                    "تحديد الملفات المطلوب تحليلها",
                    "تشغيل وكيل البحث في الملفات",
                    "تحليل المحتوى عبر Ollama",
                    "تجميع النتائج وعرضها"
                ]
            },
            "complex_task": {
                "name": "مهمة معقدة",
                "steps": [
                    "تحليل المهمة عبر Gemini API",
                    "تقسيم المهمة إلى خطوات فرعية",
                    "تنسيق الوكلاء عبر n8n",
                    "تنفيذ الخطوات عبر Ollama",
                    "حفظ السياق في AnythingLLM",
                    "تجميع النتائج النهائية"
                ]
            }
        }
        
        template = templates.get(workflow_type, {})
        return json.dumps(template, ensure_ascii=False, indent=2)
    
    def optimize_resources(self) -> Dict[str, str]:
        """تحسين استخدام الموارد"""
        recommendations = {}
        
        # فحص استخدام الذاكرة
        try:
            import psutil
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                recommendations["memory"] = "⚠️ استخدام الذاكرة عالي - فكر في إيقاف بعض النماذج"
            else:
                recommendations["memory"] = "✅ استخدام الذاكرة طبيعي"
        except ImportError:
            recommendations["memory"] = "❓ تثبيت psutil مطلوب لمراقبة الذاكرة"
        
        # فحص النماذج النشطة
        models = self.get_ollama_models()
        if len(models) > 3:
            recommendations["models"] = f"⚠️ {len(models)} نماذج متاحة - فكر في إزالة غير المستخدمة"
        else:
            recommendations["models"] = f"✅ {len(models)} نماذج متاحة"
        
        return recommendations
    
    def generate_integration_report(self) -> str:
        """إنشاء تقرير شامل عن حالة التكامل"""
        report = []
        report.append("🤖 تقرير نظام التكامل الذكي")
        report.append("=" * 50)
        
        # حالة النظام
        status = self.check_system_status()
        report.append("\n📊 حالة المكونات:")
        for component, state in status.items():
            report.append(f"  {component}: {state}")
        
        # النماذج المتاحة
        models = self.get_ollama_models()
        report.append(f"\n🧠 النماذج المتاحة ({len(models)}):")
        for model in models:
            report.append(f"  • {model}")
        
        # توصيات التحسين
        recommendations = self.optimize_resources()
        report.append("\n💡 توصيات التحسين:")
        for category, recommendation in recommendations.items():
            report.append(f"  {category}: {recommendation}")
        
        # الخطوات التالية
        report.append("\n🚀 الخطوات التالية المقترحة:")
        if "❌" in str(status.values()):
            report.append("  1. إصلاح المكونات غير المتاحة")
            report.append("  2. فحص إعدادات الشبكة والمنافذ")
            report.append("  3. إعادة تشغيل الخدمات المتوقفة")
        else:
            report.append("  1. تجربة تدفقات العمل المختلفة")
            report.append("  2. تحسين الأداء والموارد")
            report.append("  3. إضافة المزيد من الوكلاء المتخصصين")
        
        return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="AI Integration Controller")
    parser.add_argument("--status", action="store_true", help="فحص حالة النظام")
    parser.add_argument("--query", type=str, help="تشغيل استعلام Gemini")
    parser.add_argument("--agent", type=str, help="تشغيل وكيل محدد")
    parser.add_argument("--models", action="store_true", help="عرض النماذج المتاحة")
    parser.add_argument("--report", action="store_true", help="إنشاء تقرير شامل")
    parser.add_argument("--optimize", action="store_true", help="توصيات التحسين")
    
    args = parser.parse_args()
    controller = AIIntegrationController()
    
    if args.status:
        status = controller.check_system_status()
        print("📊 حالة النظام:")
        for component, state in status.items():
            print(f"  {component}: {state}")
    
    elif args.query:
        print(f"🤖 استعلام Gemini: {args.query}")
        result = controller.run_gemini_query(args.query)
        print(f"📝 النتيجة:\n{result}")
    
    elif args.agent:
        print(f"🤖 تشغيل الوكيل: {args.agent}")
        result = controller.run_agent(args.agent)
        print(result)
    
    elif args.models:
        models = controller.get_ollama_models()
        print(f"🧠 النماذج المتاحة ({len(models)}):")
        for model in models:
            print(f"  • {model}")
    
    elif args.report:
        report = controller.generate_integration_report()
        print(report)
    
    elif args.optimize:
        recommendations = controller.optimize_resources()
        print("💡 توصيات التحسين:")
        for category, recommendation in recommendations.items():
            print(f"  {category}: {recommendation}")
    
    else:
        print("🤖 مرحباً بك في نظام التكامل الذكي!")
        print("استخدم --help لعرض الخيارات المتاحة")

if __name__ == "__main__":
    main()
