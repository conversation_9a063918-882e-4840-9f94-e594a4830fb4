#!/usr/bin/env python3
"""
Gemini Connector - موصل Gemini CLI المعزول
واجهة معزولة للتواصل مع Gemini CLI لـ Augment Agent
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

class GeminiConnector:
    """موصل معزول لـ Gemini CLI"""
    
    def __init__(self):
        self.gemini_path = "C:/Users/<USER>"
        self.memory_file = Path("../shared-memory/project-knowledge.json")
        self.history_file = Path("consultation-history.json")
        self.logs_dir = Path("../logs/gemini-logs")
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
    def load_memory(self):
        """تحميل الذاكرة المشتركة"""
        if self.memory_file.exists():
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_memory(self, memory_data):
        """حفظ الذاكرة المشتركة"""
        with open(self.memory_file, 'w', encoding='utf-8') as f:
            json.dump(memory_data, f, ensure_ascii=False, indent=2)
    
    def log_consultation(self, query, response, success=True, duration=None):
        """تسجيل الاستشارة"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "query": query[:200] + "..." if len(query) > 200 else query,
            "response": response[:500] + "..." if len(response) > 500 else response,
            "success": success,
            "duration_seconds": duration,
            "session_id": datetime.now().strftime("%Y%m%d_%H%M")
        }
        
        # حفظ في تاريخ الاستشارات
        history = []
        if self.history_file.exists():
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
        
        history.append(log_entry)
        
        # الاحتفاظ بآخر 100 استشارة فقط
        if len(history) > 100:
            history = history[-100:]
        
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
        
        # تحديث الذاكرة المشتركة
        self.update_usage_stats(success, duration)
    
    def update_usage_stats(self, success, duration):
        """تحديث إحصائيات الاستخدام"""
        memory = self.load_memory()
        
        if "assistants" not in memory:
            memory["assistants"] = {}
        if "gemini_cli" not in memory["assistants"]:
            memory["assistants"]["gemini_cli"] = {}
        if "usage_stats" not in memory["assistants"]["gemini_cli"]:
            memory["assistants"]["gemini_cli"]["usage_stats"] = {
                "total_consultations": 0,
                "successful_responses": 0,
                "average_response_time": None,
                "last_consultation": None
            }
        
        stats = memory["assistants"]["gemini_cli"]["usage_stats"]
        stats["total_consultations"] += 1
        stats["last_consultation"] = datetime.now().isoformat()
        
        if success:
            stats["successful_responses"] += 1
        
        if duration:
            if stats["average_response_time"] is None:
                stats["average_response_time"] = duration
            else:
                # متوسط متحرك بسيط
                stats["average_response_time"] = (stats["average_response_time"] + duration) / 2
        
        # تحديث الحالة
        memory["assistants"]["gemini_cli"]["status"] = "متاح" if success else "خطأ في آخر استعلام"
        
        self.save_memory(memory)
    
    def consult(self, query, timeout=30):
        """استشارة Gemini CLI"""
        start_time = time.time()
        
        try:
            # تغيير المجلد إلى مجلد Gemini
            original_cwd = os.getcwd()
            os.chdir(self.gemini_path)
            
            # تشغيل الاستعلام
            result = subprocess.run(
                ["gemini", query],
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8'
            )
            
            # العودة للمجلد الأصلي
            os.chdir(original_cwd)
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                self.log_consultation(query, response, True, duration)
                return {
                    "success": True,
                    "response": response,
                    "duration": duration,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error_msg = result.stderr.strip() or "خطأ غير محدد"
                self.log_consultation(query, error_msg, False, duration)
                return {
                    "success": False,
                    "error": error_msg,
                    "duration": duration,
                    "timestamp": datetime.now().isoformat()
                }
                
        except subprocess.TimeoutExpired:
            os.chdir(original_cwd)
            duration = time.time() - start_time
            error_msg = f"انتهت مهلة الاستعلام ({timeout} ثانية)"
            self.log_consultation(query, error_msg, False, duration)
            return {
                "success": False,
                "error": error_msg,
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            os.chdir(original_cwd)
            duration = time.time() - start_time
            error_msg = f"خطأ: {str(e)}"
            self.log_consultation(query, error_msg, False, duration)
            return {
                "success": False,
                "error": error_msg,
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            }
    
    def get_consultation_history(self, limit=10):
        """الحصول على تاريخ الاستشارات"""
        if self.history_file.exists():
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
                return history[-limit:] if limit else history
        return []
    
    def get_stats(self):
        """الحصول على إحصائيات الاستخدام"""
        memory = self.load_memory()
        return memory.get("assistants", {}).get("gemini_cli", {}).get("usage_stats", {})
    
    def test_connection(self):
        """اختبار الاتصال مع Gemini CLI"""
        test_query = "مرحبا، هل تعمل بشكل صحيح؟"
        result = self.consult(test_query, timeout=10)
        
        if result["success"]:
            print("✅ Gemini CLI متصل ويعمل بشكل صحيح")
            print(f"📝 الاستجابة: {result['response'][:100]}...")
            print(f"⏱️ وقت الاستجابة: {result['duration']:.2f} ثانية")
        else:
            print("❌ فشل في الاتصال مع Gemini CLI")
            print(f"🚨 الخطأ: {result['error']}")
        
        return result["success"]

# واجهات سريعة للاستخدام
def quick_consult(query):
    """استشارة سريعة"""
    connector = GeminiConnector()
    result = connector.consult(query)
    return result["response"] if result["success"] else f"خطأ: {result['error']}"

def test_gemini():
    """اختبار سريع لـ Gemini"""
    connector = GeminiConnector()
    return connector.test_connection()

def main():
    """اختبار الموصل"""
    print("🧠 اختبار Gemini Connector")
    print("=" * 40)
    
    connector = GeminiConnector()
    
    # اختبار الاتصال
    print("\n🔍 اختبار الاتصال...")
    connector.test_connection()
    
    # عرض الإحصائيات
    print("\n📊 الإحصائيات:")
    stats = connector.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # عرض آخر الاستشارات
    print("\n📝 آخر الاستشارات:")
    history = connector.get_consultation_history(3)
    for i, consultation in enumerate(history, 1):
        timestamp = consultation["timestamp"][:16]
        query = consultation["query"][:50] + "..."
        success = "✅" if consultation["success"] else "❌"
        print(f"  {i}. {timestamp} {success} {query}")

if __name__ == "__main__":
    main()
