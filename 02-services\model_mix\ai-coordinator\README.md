# 🤖 AI Coordinator - نظا<PERSON> التنسيق الذكي

طبقة تنسيق ذكية تجمع بين Ollama و Gemini و n8n و AnythingLLM للتعاون في المهام المختلفة.

## 🎯 الهدف

إنشاء نظام ذكي يوزع المهام على النماذج المناسبة:
- **Ollama**: للمهام السريعة والمحلية
- **Gemini**: للتحليل المعقد والبحث
- **n8n**: للأتمتة والتنسيق
- **AnythingLLM**: كواجهة موحدة

## 🚀 التشغيل السريع

### 1. تثبيت Dependencies:
```bash
cd ai-coordinator
npm install
```

### 2. تكوين البيئة:
```bash
# نسخ ملف البيئة
cp .env.example .env

# تعديل المفاتيح
nano .env
```

### 3. تشغيل النظام:
```bash
# تشغيل تلقائي
chmod +x start.sh
./start.sh

# أو تشغيل يدوي
npm start
```

## 🔌 API Endpoints

### **التنسيق الذكي:**
```bash
POST http://localhost:3333/api/coordinate
{
  "prompt": "سؤالك هنا",
  "context": "السياق (اختياري)",
  "options": {
    "priority": "fast|normal|high",
    "complexity": "low|medium|high"
  }
}
```

### **التعاون بين النماذج:**
```bash
POST http://localhost:3333/api/collaborate
{
  "prompt": "سؤال معقد يحتاج عدة آراء",
  "context": "السياق"
}
```

### **فحص الحالة:**
```bash
GET http://localhost:3333/api/health
```

## 🧠 منطق اتخاذ القرار

### **متى يستخدم Ollama:**
- المهام السريعة (priority: "fast")
- النصوص القصيرة (< 100 حرف)
- المهام البرمجية (context: "code")

### **متى يستخدم Gemini:**
- التحليل المعقد (complexity: "high")
- البحث والدراسة (يحتوي على "research")
- المهام التي تحتاج دقة عالية

## 🧪 الاختبار

### اختبار سريع:
```bash
npm test
```

### اختبار يدوي:
```bash
# اختبار الحالة
curl http://localhost:3333/api/health

# اختبار بسيط
curl -X POST http://localhost:3333/api/coordinate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "قل مرحبا", "options": {"priority": "fast"}}'
```

## 📊 أمثلة الاستخدام

### **مثال 1: سؤال سريع**
```javascript
const response = await fetch('http://localhost:3333/api/coordinate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'ما هو 2+2؟',
    options: { priority: 'fast' }
  })
});
// سيستخدم Ollama للسرعة
```

### **مثال 2: تحليل معقد**
```javascript
const response = await fetch('http://localhost:3333/api/coordinate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'حلل اتجاهات الذكاء الاصطناعي في 2024',
    options: { complexity: 'high' }
  })
});
// سيستخدم Gemini للتحليل العميق
```

### **مثال 3: تعاون النماذج**
```javascript
const response = await fetch('http://localhost:3333/api/collaborate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'ما أفضل لغة برمجة للمبتدئين؟'
  })
});
// سيحصل على إجابات من Ollama و Gemini معاً
```

## 🔧 التكامل مع AnythingLLM

### **الخطوة 1: إضافة Custom Function**
في AnythingLLM، أضف Custom Function:

```javascript
// في AnythingLLM Custom Functions
async function aiCoordinator(prompt, options = {}) {
  const response = await fetch('http://localhost:3333/api/coordinate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt, options })
  });
  
  const result = await response.json();
  return `[${result.decision.model}]: ${result.response}`;
}
```

### **الخطوة 2: استخدام في المحادثة**
```
المستخدم: @aiCoordinator("اشرح الذكاء الاصطناعي", {"complexity": "high"})
النظام: [gemini]: الذكاء الاصطناعي هو...
```

## 🔄 التكامل مع n8n

### **إنشاء Webhook في n8n:**
1. أضف Webhook Node في n8n
2. اضبط URL: `http://localhost:3333/api/coordinate`
3. أضف HTTP Request Node للتواصل مع النماذج

### **مثال Workflow:**
```
Webhook → Decision → [Ollama/Gemini] → Response
```

## 📈 التطوير المستقبلي

### **المرحلة التالية:**
- [ ] إضافة Context Memory
- [ ] تحسين Decision Engine
- [ ] إضافة Learning System
- [ ] تطوير Web Dashboard
- [ ] تكامل أعمق مع VSCode

### **الميزات المخططة:**
- [ ] نظام Cache ذكي
- [ ] مراقبة الأداء
- [ ] تحليل الاستخدام
- [ ] API Rate Limiting

## 🐛 استكشاف الأخطاء

### **مشاكل شائعة:**

#### Ollama لا يعمل:
```bash
# تحقق من Ollama
curl http://localhost:11434/api/tags
```

#### Gemini API خطأ:
```bash
# تحقق من API Key في .env
echo $GEMINI_API_KEY
```

#### المنفذ مستخدم:
```bash
# تغيير المنفذ في .env
PORT=3334
```

---

**🎉 النظام جاهز للاستخدام! ابدأ بتشغيل `npm start` واختبر التكامل.**
