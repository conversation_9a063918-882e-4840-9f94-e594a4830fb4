#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 Agent Coordinator - AI Development Assistant
===============================================
منسق الوكلاء الذكي - يدير ويوجه الوكلاء المتخصصين

المميزات:
- تنسيق العمل بين الوكلاء المختلفين
- توجيه المهام للوكيل المناسب
- دمج النتائج من عدة وكلاء
- إدارة الموارد والأولويات
- واجهة موحدة للتفاعل

الوكلاء المدارين:
- 🧠 Memory Agent (gemma3n:e4b)
- 🔍 File Search Agent (llama3:8b)
- 💻 Terminal Agent (mistral:7b)
- 📊 Data Analysis Agent (phi3:mini)
"""

import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

# استيراد الوكلاء
try:
    from memory_agent import MemoryAgent
    from file_search_agent import FileSearchAgent
    from terminal_agent import TerminalAgent
    from data_analysis_agent import DataAnalysisAgent
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد بعض الوكلاء: {e}")

class TaskType(Enum):
    """أنواع المهام"""
    MEMORY = "memory"
    FILE_SEARCH = "file_search"
    TERMINAL = "terminal"
    DATA_ANALYSIS = "data_analysis"
    MIXED = "mixed"

@dataclass
class Task:
    """مهمة للوكلاء"""
    id: str
    type: TaskType
    description: str
    parameters: Dict[str, Any]
    priority: int = 1
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

class AgentCoordinator:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        تهيئة منسق الوكلاء
        
        Args:
            ollama_url: رابط خادم Ollama
        """
        self.ollama_url = ollama_url
        self.agents = {}
        self.task_queue = []
        self.completed_tasks = []
        
        # تهيئة الوكلاء
        self._initialize_agents()
        
        print("🤖 Agent Coordinator initialized")
        print(f"📋 Available agents: {list(self.agents.keys())}")
    
    def _initialize_agents(self):
        """تهيئة جميع الوكلاء"""
        try:
            self.agents['memory'] = MemoryAgent(self.ollama_url)
            print("✅ Memory Agent initialized")
        except Exception as e:
            print(f"❌ Memory Agent failed: {e}")
        
        try:
            self.agents['file_search'] = FileSearchAgent(self.ollama_url)
            print("✅ File Search Agent initialized")
        except Exception as e:
            print(f"❌ File Search Agent failed: {e}")
        
        try:
            self.agents['terminal'] = TerminalAgent(self.ollama_url)
            print("✅ Terminal Agent initialized")
        except Exception as e:
            print(f"❌ Terminal Agent failed: {e}")
        
        try:
            self.agents['data_analysis'] = DataAnalysisAgent(self.ollama_url)
            print("✅ Data Analysis Agent initialized")
        except Exception as e:
            print(f"❌ Data Analysis Agent failed: {e}")
    
    def classify_task(self, description: str) -> TaskType:
        """
        تصنيف المهمة لتحديد الوكيل المناسب
        
        Args:
            description: وصف المهمة
            
        Returns:
            نوع المهمة
        """
        description_lower = description.lower()
        
        # كلمات مفتاحية للذاكرة
        memory_keywords = ['ذاكرة', 'جلسة', 'تذكر', 'حفظ', 'استرجاع', 'memory', 'session', 'remember']
        if any(keyword in description_lower for keyword in memory_keywords):
            return TaskType.MEMORY
        
        # كلمات مفتاحية للبحث في الملفات
        file_keywords = ['ملف', 'بحث', 'كود', 'مجلد', 'file', 'search', 'code', 'directory']
        if any(keyword in description_lower for keyword in file_keywords):
            return TaskType.FILE_SEARCH
        
        # كلمات مفتاحية للترمينال
        terminal_keywords = ['أمر', 'ترمينال', 'تنفيذ', 'نظام', 'command', 'terminal', 'execute', 'system']
        if any(keyword in description_lower for keyword in terminal_keywords):
            return TaskType.TERMINAL
        
        # كلمات مفتاحية لتحليل البيانات
        data_keywords = ['بيانات', 'تحليل', 'إحصائيات', 'رسم', 'data', 'analysis', 'statistics', 'chart']
        if any(keyword in description_lower for keyword in data_keywords):
            return TaskType.DATA_ANALYSIS
        
        # افتراضي: بحث في الملفات
        return TaskType.FILE_SEARCH
    
    def add_task(self, description: str, parameters: Dict[str, Any] = None, priority: int = 1) -> str:
        """
        إضافة مهمة جديدة
        
        Args:
            description: وصف المهمة
            parameters: معاملات المهمة
            priority: أولوية المهمة (1-10)
            
        Returns:
            معرف المهمة
        """
        task_id = f"task_{int(time.time() * 1000)}"
        task_type = self.classify_task(description)
        
        task = Task(
            id=task_id,
            type=task_type,
            description=description,
            parameters=parameters or {},
            priority=priority
        )
        
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda t: t.priority, reverse=True)
        
        print(f"📝 Task added: {task_id} ({task_type.value}) - {description}")
        return task_id
    
    def execute_task(self, task: Task) -> Dict[str, Any]:
        """
        تنفيذ مهمة
        
        Args:
            task: المهمة المطلوب تنفيذها
            
        Returns:
            نتيجة التنفيذ
        """
        start_time = time.time()
        
        try:
            if task.type == TaskType.MEMORY:
                result = self._execute_memory_task(task)
            elif task.type == TaskType.FILE_SEARCH:
                result = self._execute_file_search_task(task)
            elif task.type == TaskType.TERMINAL:
                result = self._execute_terminal_task(task)
            elif task.type == TaskType.DATA_ANALYSIS:
                result = self._execute_data_analysis_task(task)
            else:
                result = {"error": f"نوع المهمة غير مدعوم: {task.type}"}
            
            execution_time = time.time() - start_time
            
            return {
                "task_id": task.id,
                "type": task.type.value,
                "description": task.description,
                "result": result,
                "execution_time": execution_time,
                "status": "success" if "error" not in result else "error"
            }
            
        except Exception as e:
            return {
                "task_id": task.id,
                "type": task.type.value,
                "description": task.description,
                "result": {"error": str(e)},
                "execution_time": time.time() - start_time,
                "status": "error"
            }
    
    def _execute_memory_task(self, task: Task) -> Dict[str, Any]:
        """تنفيذ مهمة الذاكرة"""
        if 'memory' not in self.agents:
            return {"error": "Memory Agent غير متاح"}
        
        agent = self.agents['memory']
        action = task.parameters.get('action', 'search')
        
        if action == 'search':
            query = task.parameters.get('query', task.description)
            return {"results": agent.search_memory(query)}
        elif action == 'analyze':
            session_file = task.parameters.get('session_file')
            if session_file:
                return agent.analyze_session(session_file)
            else:
                return {"error": "session_file مطلوب للتحليل"}
        elif action == 'report':
            return agent.generate_session_report()
        else:
            return {"error": f"إجراء غير مدعوم: {action}"}
    
    def _execute_file_search_task(self, task: Task) -> Dict[str, Any]:
        """تنفيذ مهمة البحث في الملفات"""
        if 'file_search' not in self.agents:
            return {"error": "File Search Agent غير متاح"}
        
        agent = self.agents['file_search']
        action = task.parameters.get('action', 'search')
        
        if action == 'search':
            query = task.parameters.get('query', task.description)
            directory = task.parameters.get('directory', '.')
            return {"results": agent.search_in_files(query, directory)}
        elif action == 'analyze':
            file_path = task.parameters.get('file_path')
            if file_path:
                return agent.analyze_code_file(file_path)
            else:
                return {"error": "file_path مطلوب للتحليل"}
        elif action == 'scan':
            directory = task.parameters.get('directory', '.')
            return {"files": agent.scan_directory(directory)}
        elif action == 'summary':
            directory = task.parameters.get('directory', '.')
            return agent.generate_project_summary(directory)
        else:
            return {"error": f"إجراء غير مدعوم: {action}"}
    
    def _execute_terminal_task(self, task: Task) -> Dict[str, Any]:
        """تنفيذ مهمة الترمينال"""
        if 'terminal' not in self.agents:
            return {"error": "Terminal Agent غير متاح"}
        
        agent = self.agents['terminal']
        action = task.parameters.get('action', 'analyze')
        
        if action == 'analyze':
            command = task.parameters.get('command', task.description)
            return agent.analyze_command(command)
        elif action == 'suggest':
            return agent.suggest_command(task.description)
        elif action == 'execute':
            command = task.parameters.get('command')
            confirm = task.parameters.get('confirm_dangerous', False)
            if command:
                return agent.execute_command(command, confirm)
            else:
                return {"error": "command مطلوب للتنفيذ"}
        elif action == 'system_info':
            return agent.get_system_info()
        elif action == 'processes':
            pattern = task.parameters.get('pattern')
            return {"processes": agent.monitor_processes(pattern)}
        else:
            return {"error": f"إجراء غير مدعوم: {action}"}
    
    def _execute_data_analysis_task(self, task: Task) -> Dict[str, Any]:
        """تنفيذ مهمة تحليل البيانات"""
        if 'data_analysis' not in self.agents:
            return {"error": "Data Analysis Agent غير متاح"}
        
        agent = self.agents['data_analysis']
        action = task.parameters.get('action', 'analyze')
        
        if action == 'analyze':
            file_path = task.parameters.get('file_path')
            if file_path:
                return agent.analyze_file(file_path)
            else:
                return {"error": "file_path مطلوب للتحليل"}
        elif action == 'compare':
            file1 = task.parameters.get('file1')
            file2 = task.parameters.get('file2')
            if file1 and file2:
                return agent.compare_datasets(file1, file2)
            else:
                return {"error": "file1 و file2 مطلوبان للمقارنة"}
        elif action == 'visualize':
            # يحتاج تنفيذ إضافي
            return {"message": "التصور البياني قيد التطوير"}
        else:
            return {"error": f"إجراء غير مدعوم: {action}"}
    
    def process_queue(self) -> List[Dict[str, Any]]:
        """
        معالجة قائمة المهام
        
        Returns:
            نتائج جميع المهام
        """
        results = []
        
        while self.task_queue:
            task = self.task_queue.pop(0)
            print(f"🔄 Processing task: {task.id}")
            
            result = self.execute_task(task)
            results.append(result)
            self.completed_tasks.append(task)
            
            print(f"✅ Task completed: {task.id} ({result['status']})")
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """
        الحصول على حالة المنسق
        
        Returns:
            معلومات الحالة
        """
        return {
            "available_agents": list(self.agents.keys()),
            "queue_size": len(self.task_queue),
            "completed_tasks": len(self.completed_tasks),
            "ollama_url": self.ollama_url
        }

def main():
    """دالة الاختبار الرئيسية"""
    print("🤖 Agent Coordinator - AI Development Assistant")
    print("=" * 50)
    
    # تهيئة المنسق
    coordinator = AgentCoordinator()
    
    # إضافة مهام تجريبية
    print("\n📝 إضافة مهام تجريبية:")
    
    coordinator.add_task("البحث عن ملفات Python في المشروع", 
                        {"action": "search", "query": "python", "directory": "."})
    
    coordinator.add_task("تحليل استخدام الذاكرة في النظام",
                        {"action": "system_info"})
    
    coordinator.add_task("البحث في الذاكرة عن جلسات Docker",
                        {"action": "search", "query": "docker"})
    
    # معالجة المهام
    print("\n🔄 معالجة المهام:")
    results = coordinator.process_queue()
    
    # عرض النتائج
    print(f"\n📊 تم إنجاز {len(results)} مهام:")
    for result in results:
        print(f"- {result['task_id']}: {result['status']} ({result['execution_time']:.2f}s)")
    
    # عرض الحالة
    print(f"\n📈 الحالة النهائية:")
    status = coordinator.get_status()
    print(f"الوكلاء المتاحين: {status['available_agents']}")
    print(f"المهام المكتملة: {status['completed_tasks']}")

if __name__ == "__main__":
    main()
