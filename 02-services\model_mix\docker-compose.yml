version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # Basic configuration
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}

      # Database configuration (using SQLite by default)
      - DB_TYPE=${DB_TYPE}
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

      # General settings
      - N8N_HOST=${N8N_HOST}
      - N8N_PORT=${N8N_PORT}
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=${WEBHOOK_URL}

      # Security settings
      - N8N_SECURE_COOKIE=false
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}

      # Timezone
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
      
    volumes:
      # Persist n8n data
      - n8n_data:/home/<USER>/.n8n
      
      # Mount local files if needed (optional)
      # - ./n8n-local-files:/files
      
    # Optional: Add health check
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  n8n_data:
    driver: local
