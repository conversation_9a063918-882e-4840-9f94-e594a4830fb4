# إعد<PERSON> خوادم MCP - سجل المحادثة
# =====================================

## 📅 تاريخ الإنشاء: 2025-07-05

## 🎯 الهدف من المشروع:
إنشاء مجلد منفصل لخوادم MCP خارج مشروع AnythingLLM لتحسين التنظيم والإدارة.

## 📍 المسارات المنشأة:

### المجلد الرئيسي:
- **مسار MCP Servers**: `C:\Users\<USER>\mcp-servers`
- **مسار AnythingLLM**: `C:\Users\<USER>\anything llm`
- **مسار Memory**: `C:\Users\<USER>\anything llm\memory`

### الملفات المنشأة في mcp-servers:
1. `docker-compose.yml` - إعداد Docker Compose للخوادم
2. `.env` - متغيرات البيئة والتوكنات
3. `manage-mcp.ps1` - سكريبت PowerShell لإدارة الخوادم
4. `README.md` - دليل الاستخدام الشامل
5. `start.txt` - ملف التوثيق والحالة
6. `shared/` - مجلد مشترك للملفات
7. `configs/` - إعدادات الخوادم
8. `logs/` - سجلات النظام

## 🖥️ خوادم MCP المتاحة:

| الخادم | المنفذ | الوصف | الحالة |
|--------|--------|-------|--------|
| GitHub MCP | 8801 | إدارة مستودعات GitHub | جاهز |
| Filesystem MCP | 8802 | إدارة نظام الملفات | جاهز |
| DuckDuckGo MCP | 8803 | البحث في الإنترنت | جاهز |
| AWS Diagram MCP | 8804 | إنشاء مخططات AWS | جاهز |
| Desktop Commander | 8805 | تنفيذ أوامر النظام | جاهز |
| OpenAPI MCP | 8806 | إدارة مخططات API | جاهز |
| Azure MCP | 8807 | خدمات Microsoft Azure | جاهز |
| PostgreSQL MCP | 8808 | قاعدة بيانات PostgreSQL | جاهز |

## ⚙️ أوامر الإدارة الرئيسية:

### تشغيل الخوادم:
```powershell
cd C:\Users\<USER>\mcp-servers
.\manage-mcp.ps1 -Action start
```

### عرض الحالة:
```powershell
.\manage-mcp.ps1 -Action status
```

### عرض السجلات:
```powershell
.\manage-mcp.ps1 -Action logs
```

### إيقاف الخوادم:
```powershell
.\manage-mcp.ps1 -Action stop
```

### تحديث الخوادم:
```powershell
.\manage-mcp.ps1 -Action update
```

## 🔗 ربط مع AnythingLLM:

### خطوات الربط:
1. فتح AnythingLLM على: `http://localhost:3001`
2. الذهاب إلى: Settings → Integrations
3. إضافة خوادم MCP بالمنافذ التالية:
   - GitHub: `http://localhost:8801`
   - Filesystem: `http://localhost:8802`
   - DuckDuckGo: `http://localhost:8803`
   - AWS Diagram: `http://localhost:8804`
   - Desktop Commander: `http://localhost:8805`
   - OpenAPI: `http://localhost:8806`
   - Azure: `http://localhost:8807`
   - PostgreSQL: `http://localhost:8808`

## 🛠️ إعدادات Docker Desktop:

### المزايا:
- ✅ خوادم منفصلة عن AnythingLLM
- ✅ إدارة مستقلة لكل خادم
- ✅ شبكة Docker معزولة (mcp-network)
- ✅ سكريبت إدارة شامل
- ✅ توثيق كامل

### الشبكة:
- **اسم الشبكة**: `mcp-network`
- **نوع الشبكة**: `bridge`
- **العزل**: منفصلة عن شبكات أخرى

## 📋 الخطوات التالية المطلوبة:

### 1. تحديث متغيرات البيئة:
```bash
# في ملف .env
GITHUB_TOKEN=your_actual_github_token
AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=your_database_name
```

### 2. تشغيل الخوادم لأول مرة:
```powershell
cd C:\Users\<USER>\mcp-servers
.\manage-mcp.ps1 -Action start
```

### 3. فحص الحالة:
```powershell
.\manage-mcp.ps1 -Action status
```

### 4. ربط مع AnythingLLM:
- إضافة خوادم MCP في إعدادات AnythingLLM

## 🔧 استكشاف الأخطاء:

### مشاكل شائعة وحلولها:
1. **مشكلة المنافذ**: فحص المنافذ المستخدمة
2. **مشكلة الأذونات**: تشغيل PowerShell كمدير
3. **مشكلة Docker**: إعادة تشغيل Docker Desktop
4. **مشكلة الشبكة**: فحص إعدادات الشبكة

## 📝 ملاحظات مهمة:

### الأمان:
- تأكد من تحديث جميع التوكنات في ملف `.env`
- لا تشارك ملف `.env` مع أحد
- استخدم توكنات محدودة الصلاحيات

### الأداء:
- مراقبة استخدام الموارد بـ `docker stats`
- تنظيف الصور غير المستخدمة دورياً
- مراجعة السجلات بانتظام

### الصيانة:
- تحديث الخوادم دورياً
- نسخ احتياطي لملفات الإعداد
- مراقبة حالة الخوادم

## ✅ حالة المشروع:
- [x] إنشاء هيكل المجلدات
- [x] إعداد Docker Compose
- [x] إنشاء سكريبت الإدارة
- [x] كتابة التوثيق
- [x] إنشاء ملف Memory
- [x] تحديث متغيرات البيئة
- [x] تشغيل الخوادم لأول مرة
- [ ] ربط مع AnythingLLM

## 🎉 تحديث: تم تشغيل الخوادم بنجاح! (2025-07-05)

### الخوادم المشغلة حالياً:
- ✅ Simple MCP Server (8801) - خادم أساسي للاختبار
- ✅ Filesystem MCP Server (8802) - إدارة الملفات
- ✅ Web Search MCP Server (8803) - البحث في الإنترنت
- ✅ AWS Diagram MCP Server (8804) - مخططات AWS
- ✅ Docker MCP Server (8811) - إدارة Docker

### اختبار الخوادم:
جميع الخوادم تستجيب بشكل صحيح على المنافذ المحددة وتعيد استجابات JSON صحيحة.
