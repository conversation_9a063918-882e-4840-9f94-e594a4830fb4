{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🤖 Google Gemini API Test Notebook\n", "\n", "هذا الـ Notebook لاختبار Google Gemini API"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تثبيت المكتبات المطلوبة\n", "!pip install google-generativeai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import google.generativeai as genai\n", "from google.generativeai.types import HarmCategory, HarmBlockThreshold\n", "\n", "# إعداد API Key\n", "# ضع مفتاح API الخاص بك هنا\n", "GEMINI_API_KEY = \"your-api-key-here\"\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "print(\"✅ تم إعداد Gemini API\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# عرض النماذج المتاحة\n", "print(\"📋 النماذج المتاحة:\")\n", "for model in genai.list_models():\n", "    if 'generateContent' in model.supported_generation_methods:\n", "        print(f\"  - {model.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# إنشاء نموذج\n", "model = genai.GenerativeModel('gemini-pro')\n", "\n", "# اختبار توليد النص\n", "prompt = \"اشرح لي ما هو الذكاء الاصطناعي بطريقة بسيطة\"\n", "\n", "response = model.generate_content(prompt)\n", "print(\"🤖 إجابة Gemini:\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# اختبار المحادثة التفاعلية\n", "chat = model.start_chat(history=[])\n", "\n", "# إرسال رسائل متعددة\n", "messages = [\n", "    \"مرحبا، ما اسمك؟\",\n", "    \"ما هي قدراتك في البرمجة؟\",\n", "    \"اكتب لي كود Python بسيط\"\n", "]\n", "\n", "for message in messages:\n", "    print(f\"👤 المستخدم: {message}\")\n", "    response = chat.send_message(message)\n", "    print(f\"🤖 Gemini: {response.text}\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# اختبار مع إعدادات متقدمة\n", "generation_config = genai.types.GenerationConfig(\n", "    temperature=0.9,\n", "    top_p=1,\n", "    top_k=1,\n", "    max_output_tokens=2048,\n", ")\n", "\n", "safety_settings = {\n", "    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "}\n", "\n", "model_advanced = genai.GenerativeModel(\n", "    model_name='gemini-pro',\n", "    generation_config=generation_config,\n", "    safety_settings=safety_settings\n", ")\n", "\n", "response = model_advanced.generate_content(\"اكتب قصة قصيرة عن المستقبل\")\n", "print(\"📖 قصة من Gemini:\")\n", "print(response.text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}