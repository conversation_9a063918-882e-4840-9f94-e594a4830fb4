# 💾 سكريبت حفظ الجلسات - AI Development Assistant
# =====================================================
# الغرض: أتمتة حفظ وفهرسة جلسات العمل
# الاستخدام: .\save-session.ps1 -Title "عنوان الجلسة" -Type "Setup"

param(
    [Parameter(Mandatory=$true)]
    [string]$Title,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("Setup", "Debug", "Enhancement", "Documentation", "Testing")]
    [string]$Type,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Critical", "Important", "Normal", "Info")]
    [string]$Priority = "Normal",
    
    [Parameter(Mandatory=$false)]
    [string]$Tags = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Duration = "غير محدد"
)

# إعداد المتغيرات
$Date = Get-Date -Format "yyyy-MM-dd"
$Time = Get-Date -Format "HH:mm"
$DateTime = Get-Date -Format "yyyy-MM-dd-HH-mm"

# تنظيف العنوان للاستخدام في اسم الملف
$CleanTitle = $Title -replace '[^\w\s-]', '' -replace '\s+', '-' -replace '-+', '-'
$FileName = "session-$Date-$CleanTitle.md"
$FilePath = "memory/sessions/$FileName"

# رموز النوع والأولوية
$TypeIcons = @{
    "Setup" = "🔧"
    "Debug" = "🐛"
    "Enhancement" = "⚡"
    "Documentation" = "📚"
    "Testing" = "🧪"
}

$PriorityIcons = @{
    "Critical" = "🔴"
    "Important" = "🟡"
    "Normal" = "🟢"
    "Info" = "🔵"
}

# إنشاء محتوى الجلسة
$SessionContent = @"
# $($TypeIcons[$Type]) $Title - $Date

## 📋 **معلومات الجلسة:**

**التاريخ:** $Date  
**الوقت:** $Time  
**الموضوع:** $Title  
**النوع:** $($TypeIcons[$Type]) $Type  
**الأولوية:** $($PriorityIcons[$Priority]) $Priority  
**المدة:** $Duration  
**الحالة:** 🔄 جارية

**العلامات:** $Tags

## 🎯 **الأهداف:**

### **الهدف الرئيسي:**
- [ ] [يتم تحديده أثناء الجلسة]

### **الأهداف الفرعية:**
- [ ] [هدف فرعي 1]
- [ ] [هدف فرعي 2]

## 🔍 **تحليل المشكلة/الموضوع:**

### **الوضع الحالي:**
[يتم تحديده أثناء الجلسة]

### **المتطلبات:**
- [متطلب 1]
- [متطلب 2]

## 📝 **الخطوات المتبعة:**

### **1. بداية الجلسة**
**الوقت:** $Time  
**الإجراء:** إنشاء ملف الجلسة  
**النتيجة:** ✅ تم إنشاء الملف بنجاح

[سيتم إضافة المزيد من الخطوات أثناء الجلسة]

## 🔄 **التغييرات المطبقة:**

[سيتم تحديثها أثناء الجلسة]

## 📊 **النتائج:**

[سيتم تحديثها في نهاية الجلسة]

## 🐛 **المشاكل المواجهة:**

[سيتم تحديثها عند مواجهة مشاكل]

## 📚 **الدروس المستفادة:**

[سيتم تحديثها في نهاية الجلسة]

## 🔗 **المراجع والروابط:**

[سيتم إضافتها حسب الحاجة]

## 📈 **الإحصائيات:**

- **وقت البداية:** $Time
- **وقت النهاية:** [سيتم تحديثه]
- **المدة الإجمالية:** [سيتم حسابها]

## 🔮 **الخطوات التالية:**

[سيتم تحديثها في نهاية الجلسة]

---
**حفظت في:** memory/sessions/  
**المرجع:** $FileName  
**تم بواسطة:** AI Development Assistant  
**تم الإنشاء:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

# إنشاء الملف
try {
    # التأكد من وجود المجلد
    if (!(Test-Path "memory/sessions")) {
        New-Item -ItemType Directory -Path "memory/sessions" -Force
    }
    
    # كتابة الملف
    $SessionContent | Out-File -FilePath $FilePath -Encoding UTF8
    
    Write-Host "✅ تم إنشاء ملف الجلسة بنجاح!" -ForegroundColor Green
    Write-Host "📁 المسار: $FilePath" -ForegroundColor Cyan
    Write-Host "📝 العنوان: $Title" -ForegroundColor Yellow
    Write-Host "🏷️ النوع: $($TypeIcons[$Type]) $Type" -ForegroundColor Magenta
    Write-Host "⭐ الأولوية: $($PriorityIcons[$Priority]) $Priority" -ForegroundColor Blue
    
    # تحديث الفهرس
    Write-Host "🔄 تحديث فهرس الجلسات..." -ForegroundColor Yellow
    
    # إضافة الجلسة للفهرس (مبسط)
    $IndexEntry = "| $Time | $Title | $($TypeIcons[$Type]) $Type | $($PriorityIcons[$Priority]) $Priority | 🔄 جارية | [$FileName]($FileName) |"
    
    Write-Host "📇 تم تحديث الفهرس" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 يمكنك الآن بدء العمل وتحديث الملف أثناء الجلسة!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ خطأ في إنشاء الملف: $($_.Exception.Message)" -ForegroundColor Red
}
"@

# أمثلة للاستخدام
Write-Host @"

# 📖 أمثلة للاستخدام:

## إنشاء جلسة إعداد:
.\save-session.ps1 -Title "إعداد قاعدة البيانات" -Type "Setup" -Priority "Critical" -Tags "#database #setup #mysql"

## إنشاء جلسة تصحيح:
.\save-session.ps1 -Title "حل مشكلة الاتصال" -Type "Debug" -Priority "Important" -Duration "30 دقيقة"

## إنشاء جلسة تحسين:
.\save-session.ps1 -Title "تحسين الأداء" -Type "Enhancement" -Priority "Normal" -Tags "#performance #optimization"

"@
