#!/usr/bin/env python3
"""
Google Gemini API Test Script
اختبار نموذج Gemini من Google
"""

import os
import sys
import json
from datetime import datetime

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
except ImportError:
    print("❌ Google Generative AI library not installed")
    print("Run: pip install google-generativeai")
    sys.exit(1)

def setup_gemini():
    """إعداد Gemini API"""
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not api_key:
        print("❌ لم يتم العثور على مفتاح API")
        print("يرجى تعيين GEMINI_API_KEY أو GOOGLE_API_KEY في ملف .env")
        return None
    
    genai.configure(api_key=api_key)
    print("✅ تم إعداد Gemini API بنجاح")
    return True

def list_models():
    """عرض النماذج المتاحة"""
    print("\n📋 النماذج المتاحة:")
    try:
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"  - {model.name}")
                print(f"    الوصف: {model.display_name}")
                print(f"    الإصدار: {model.version}")
                print()
    except Exception as e:
        print(f"❌ خطأ في جلب النماذج: {e}")

def test_text_generation(model_name="gemini-pro"):
    """اختبار توليد النص"""
    print(f"\n🧪 اختبار توليد النص باستخدام {model_name}")
    
    try:
        model = genai.GenerativeModel(model_name)
        
        # إعداد الأمان
        safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
        
        # اختبار باللغة العربية
        prompt = "اشرح لي ما هو الذكاء الاصطناعي بطريقة بسيطة"
        
        print(f"📝 السؤال: {prompt}")
        print("⏳ جاري التوليد...")
        
        response = model.generate_content(
            prompt,
            safety_settings=safety_settings,
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,
                top_p=0.8,
                top_k=40,
                max_output_tokens=1000,
            )
        )
        
        print("✅ الإجابة:")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد النص: {e}")
        return False

def test_chat_session():
    """اختبار جلسة المحادثة"""
    print("\n💬 اختبار جلسة المحادثة")
    
    try:
        model = genai.GenerativeModel('gemini-pro')
        chat = model.start_chat(history=[])
        
        messages = [
            "مرحبا، ما اسمك؟",
            "ما هي قدراتك؟",
            "هل يمكنك مساعدتي في البرمجة؟"
        ]
        
        for message in messages:
            print(f"\n👤 المستخدم: {message}")
            response = chat.send_message(message)
            print(f"🤖 Gemini: {response.text}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحادثة: {e}")
        return False

def save_test_results():
    """حفظ نتائج الاختبار"""
    results = {
        "timestamp": datetime.now().isoformat(),
        "status": "success",
        "models_tested": ["gemini-pro"],
        "features_tested": ["text_generation", "chat_session"]
    }
    
    with open('/app/workspace/test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("💾 تم حفظ نتائج الاختبار في /app/workspace/test_results.json")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار Google Gemini")
    print("=" * 50)
    
    # إعداد API
    if not setup_gemini():
        return
    
    # عرض النماذج
    list_models()
    
    # اختبار توليد النص
    if test_text_generation():
        print("✅ اختبار توليد النص نجح")
    else:
        print("❌ اختبار توليد النص فشل")
    
    # اختبار المحادثة
    if test_chat_session():
        print("✅ اختبار المحادثة نجح")
    else:
        print("❌ اختبار المحادثة فشل")
    
    # حفظ النتائج
    save_test_results()
    
    print("\n🎉 انتهى الاختبار!")

if __name__ == "__main__":
    main()
