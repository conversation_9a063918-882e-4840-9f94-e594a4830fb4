# AI Integration System - Quick Start
# نظام التكامل الذكي - البدء السريع

param(
    [string]$Action = "menu"
)

# إعداد الألوان والتنسيق
$Host.UI.RawUI.WindowTitle = "🤖 AI Integration System"

function Write-Banner {
    Clear-Host
    Write-Host "🤖 AI Integration System - Quick Start" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host "نظام التكامل الذكي - البدء السريع" -ForegroundColor Yellow
    Write-Host ""
}

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $color = switch ($Status) {
        "SUCCESS" { "Green" }
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        default { "White" }
    }
    
    $icon = switch ($Status) {
        "SUCCESS" { "✅" }
        "ERROR" { "❌" }
        "WARNING" { "⚠️" }
        default { "ℹ️" }
    }
    
    Write-Host "$icon $Message" -ForegroundColor $color
}

function Test-Prerequisites {
    Write-Status "فحص المتطلبات الأساسية..." "INFO"
    
    $allGood = $true
    
    # فحص Python
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion) {
            Write-Status "Python: $pythonVersion" "SUCCESS"
        } else {
            Write-Status "Python غير مثبت" "ERROR"
            $allGood = $false
        }
    } catch {
        Write-Status "Python غير متاح" "ERROR"
        $allGood = $false
    }
    
    # فحص PowerShell
    $psVersion = $PSVersionTable.PSVersion
    Write-Status "PowerShell: $psVersion" "SUCCESS"
    
    # فحص VS Code
    try {
        $codeVersion = code --version 2>$null
        if ($codeVersion) {
            Write-Status "VS Code: متاح" "SUCCESS"
        } else {
            Write-Status "VS Code غير متاح" "WARNING"
        }
    } catch {
        Write-Status "VS Code غير متاح" "WARNING"
    }
    
    # فحص Gemini CLI
    try {
        Push-Location "C:\Users\<USER>\ai-integration-system.json",
        "config\.env.integration", 
        "scripts\ai_integration_controller.py",
        "scripts\ai-integration-manager.ps1",
        "docs\AI-INTEGRATION-README.md"
    )
    
    Write-Host "📋 الملفات المهمة:" -ForegroundColor Yellow
    foreach ($file in $importantFiles) {
        if (Test-Path $file) {
            Write-Status "$file" "SUCCESS"
        } else {
            Write-Status "$file" "ERROR"
        }
    }
}

function Start-QuickTest {
    Write-Status "تشغيل اختبار سريع..." "INFO"
    Write-Host ""
    
    # اختبار Python Controller
    Write-Status "اختبار Python Controller..." "INFO"
    try {
        python scripts\ai_integration_controller.py --status
        Write-Status "Python Controller: يعمل" "SUCCESS"
    } catch {
        Write-Status "Python Controller: فشل" "ERROR"
    }
    
    Write-Host ""
    
    # اختبار Gemini CLI
    Write-Status "اختبار Gemini CLI..." "INFO"
    try {
        Push-Location "C:\Users\<USER>\ai_integration_controller.py --status
            }
            "2" { 
                $query = Read-Host "🤖 أدخل استعلامك"
                python scripts\ai_integration_controller.py --query $query
            }
            "3" { 
                python scripts\ai_integration_controller.py --models
            }
            "4" { 
                Write-Status "فتح واجهات الخدمات..." "INFO"
                Start-Process "http://localhost:4001"  # AnythingLLM
                Start-Process "http://localhost:5678"  # n8n
            }
            "5" { 
                code docs\AI-INTEGRATION-README.md
            }
            "6" { 
                .\scripts\ai-integration-manager.ps1
            }
            "7" { 
                Start-QuickTest
            }
            "0" { 
                Write-Status "شكراً لاستخدام نظام التكامل الذكي!" "SUCCESS"
                exit 
            }
            default { 
                Write-Status "اختيار غير صحيح" "ERROR"
            }
        }
        
        if ($choice -ne "0") {
            Read-Host "`n⏸️ اضغط Enter للمتابعة"
            Write-Banner
            Show-QuickActions
        }
        
    } while ($choice -ne "0")
}

function Start-FullSetup {
    Write-Status "بدء الإعداد الكامل..." "INFO"
    
    # إنشاء المجلدات المطلوبة
    $folders = @("logs", "tests\results", "config\backup")
    foreach ($folder in $folders) {
        if (-not (Test-Path $folder)) {
            New-Item -ItemType Directory -Path $folder -Force | Out-Null
            Write-Status "تم إنشاء مجلد: $folder" "SUCCESS"
        }
    }
    
    # نسخ ملفات التكوين إذا لم تكن موجودة
    if (-not (Test-Path "config\.env.local")) {
        Copy-Item "config\.env.integration" "config\.env.local"
        Write-Status "تم إنشاء ملف التكوين المحلي" "SUCCESS"
    }
    
    Write-Status "تم الإعداد الكامل بنجاح!" "SUCCESS"
}

# تشغيل الإجراء المطلوب
switch ($Action.ToLower()) {
    "setup" { 
        Write-Banner
        Start-FullSetup
    }
    "test" { 
        Write-Banner
        Test-Prerequisites
        Write-Host ""
        Test-Services
        Write-Host ""
        Start-QuickTest
    }
    "info" { 
        Write-Banner
        Show-SystemInfo
    }
    default { 
        Write-Banner
        
        # فحص سريع
        if (Test-Prerequisites) {
            Write-Status "المتطلبات الأساسية: متوفرة" "SUCCESS"
        } else {
            Write-Status "بعض المتطلبات مفقودة" "WARNING"
        }
        
        Write-Host ""
        Test-Services
        
        Show-QuickActions
    }
}
