version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      # Persist Ollama data and models
      - ollama_data:/root/.ollama
      
      # Optional: Mount local models directory if you want to share models
      # - ./models:/root/.ollama/models
      
    environment:
      # Optional: Set Ollama host
      - OLLAMA_HOST=0.0.0.0:11434
      
      # Optional: Enable debug logging
      # - OLLAMA_DEBUG=1
      
    # GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Ollama Web UI
  ollama-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: ollama-webui
    restart: unless-stopped
    ports:
      - "3000:8080"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - WEBUI_SECRET_KEY=your-secret-key-here
    volumes:
      - ollama_webui_data:/app/backend/data
    depends_on:
      - ollama

volumes:
  ollama_data:
    driver: local
  ollama_webui_data:
    driver: local
