#!/bin/bash

echo "🚀 Starting Ollama with Docker..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

# Start Ollama services
echo "📦 Starting Ollama containers..."
docker-compose up -d

# Wait a moment for containers to start
sleep 10

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Ollama is running successfully!"
    echo ""
    echo "🔗 Services:"
    echo "  - Ollama API: http://localhost:11434"
    echo "  - Web UI: http://localhost:3000"
    echo ""
    echo "📋 Useful commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo "  - Copy local models: ./copy-models.sh"
    echo ""
    echo "🔧 Model management:"
    echo "  - List models: docker exec ollama ollama list"
    echo "  - Pull model: docker exec ollama ollama pull model-name"
    echo "  - Run model: docker exec -it ollama ollama run model-name"
    echo ""
    
    # Ask if user wants to copy local models
    read -p "❓ Do you want to copy models from local Ollama installation? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        chmod +x copy-models.sh
        ./copy-models.sh
    fi
    
else
    echo "❌ Failed to start Ollama. Check the logs:"
    docker-compose logs
fi
