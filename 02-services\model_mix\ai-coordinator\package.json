{"name": "ai-coordinator", "version": "1.0.0", "description": "AI Models Coordination Layer for AnythingLLM Integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "ws": "^8.14.2", "dotenv": "^16.3.1", "body-parser": "^1.20.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["ai", "llm", "coordination", "ollama", "gemini", "n8n", "anythingllm"], "author": "AI Coordinator", "license": "MIT"}