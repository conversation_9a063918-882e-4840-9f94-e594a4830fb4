#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💻 Terminal Agent - AI Development Assistant
============================================
وكيل متخصص في تنفيذ أوامر الترمينال والنظام باستخدام Ollama

المميزات:
- تحليل وتفسير أوامر الترمينال
- اقتراح أوامر بديلة وآمنة
- شرح نتائج الأوامر
- مراقبة العمليات
- إدارة النظام الذكية
- كشف الأوامر الخطيرة

النموذج المستخدم: mistral:7b (سريع للمهام التفاعلية)
"""

import json
import requests
import subprocess
import os
import platform
import psutil
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

class TerminalAgent:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        تهيئة وكيل الترمينال
        
        Args:
            ollama_url: رابط خادم Ollama
        """
        self.ollama_url = ollama_url
        self.model = "mistral:7b"  # نموذج سريع للتفاعل
        self.os_type = platform.system().lower()
        self.shell = self._detect_shell()
        
        # الأوامر الخطيرة التي تحتاج تأكيد
        self.dangerous_commands = [
            'rm -rf', 'del /s', 'format', 'fdisk', 'mkfs',
            'dd if=', 'shutdown', 'reboot', 'halt',
            'chmod 777', 'chown -R', 'sudo rm',
            'DROP DATABASE', 'DROP TABLE'
        ]
        
        print(f"💻 Terminal Agent initialized with model: {self.model}")
        print(f"🖥️ OS: {self.os_type}, Shell: {self.shell}")
    
    def _detect_shell(self) -> str:
        """كشف نوع الشل المستخدم"""
        if self.os_type == "windows":
            return "powershell"
        else:
            return os.environ.get("SHELL", "/bin/bash").split("/")[-1]
    
    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """
        استدعاء نموذج Ollama
        
        Args:
            prompt: النص المطلوب معالجته
            system_prompt: تعليمات النظام
            
        Returns:
            الرد من النموذج
        """
        try:
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                return f"خطأ في الاتصال: {response.status_code}"
                
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def _is_dangerous_command(self, command: str) -> bool:
        """
        فحص ما إذا كان الأمر خطير
        
        Args:
            command: الأمر المطلوب فحصه
            
        Returns:
            True إذا كان الأمر خطير
        """
        command_lower = command.lower()
        return any(dangerous in command_lower for dangerous in self.dangerous_commands)
    
    def analyze_command(self, command: str) -> Dict[str, Any]:
        """
        تحليل أمر ترمينال
        
        Args:
            command: الأمر المطلوب تحليله
            
        Returns:
            تحليل شامل للأمر
        """
        system_prompt = f"""أنت خبير في أوامر {self.shell} على نظام {self.os_type}.
حلل الأمر التالي وقدم:
1. شرح ما يفعله الأمر
2. المعاملات والخيارات المستخدمة
3. النتائج المتوقعة
4. المخاطر المحتملة (إن وجدت)
5. أوامر بديلة أو تحسينات مقترحة

كن دقيق ومفصل في التحليل."""

        analysis = self._call_ollama(f"الأمر: {command}", system_prompt)
        
        is_dangerous = self._is_dangerous_command(command)
        
        return {
            "command": command,
            "analysis": analysis,
            "is_dangerous": is_dangerous,
            "os_type": self.os_type,
            "shell": self.shell,
            "timestamp": time.time()
        }
    
    def suggest_command(self, task_description: str) -> Dict[str, Any]:
        """
        اقتراح أمر لمهمة معينة
        
        Args:
            task_description: وصف المهمة المطلوبة
            
        Returns:
            اقتراحات الأوامر
        """
        system_prompt = f"""أنت خبير في أوامر {self.shell} على نظام {self.os_type}.
اقترح أفضل الأوامر لتنفيذ المهمة التالية:

قدم:
1. الأمر الأساسي المقترح
2. أوامر بديلة (إن وجدت)
3. شرح كل أمر
4. تحذيرات أو احتياطات
5. أمثلة للاستخدام

ركز على الأوامر الآمنة والفعالة."""

        suggestions = self._call_ollama(f"المهمة: {task_description}", system_prompt)
        
        return {
            "task": task_description,
            "suggestions": suggestions,
            "os_type": self.os_type,
            "shell": self.shell,
            "timestamp": time.time()
        }
    
    def execute_command(self, command: str, confirm_dangerous: bool = False) -> Dict[str, Any]:
        """
        تنفيذ أمر ترمينال
        
        Args:
            command: الأمر المطلوب تنفيذه
            confirm_dangerous: تأكيد تنفيذ الأوامر الخطيرة
            
        Returns:
            نتيجة التنفيذ
        """
        # فحص الأمان
        if self._is_dangerous_command(command) and not confirm_dangerous:
            return {
                "command": command,
                "status": "blocked",
                "message": "أمر خطير! يحتاج تأكيد صريح",
                "is_dangerous": True
            }
        
        try:
            # تنفيذ الأمر
            if self.os_type == "windows":
                result = subprocess.run(
                    ["powershell", "-Command", command],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            else:
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            
            return {
                "command": command,
                "status": "success" if result.returncode == 0 else "error",
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": time.time()
            }
            
        except subprocess.TimeoutExpired:
            return {
                "command": command,
                "status": "timeout",
                "message": "انتهت مهلة التنفيذ (30 ثانية)"
            }
        except Exception as e:
            return {
                "command": command,
                "status": "error",
                "message": str(e)
            }
    
    def explain_output(self, command: str, output: str, error: str = "") -> str:
        """
        شرح نتيجة تنفيذ أمر
        
        Args:
            command: الأمر المنفذ
            output: النتيجة العادية
            error: رسائل الخطأ
            
        Returns:
            شرح النتيجة
        """
        system_prompt = """أنت خبير في تفسير نتائج أوامر الترمينال.
اشرح النتيجة بطريقة واضحة ومفهومة:
1. ما حدث بالضبط
2. معنى النتائج
3. ما إذا كان هناك أخطاء وكيفية حلها
4. الخطوات التالية المقترحة

اجعل الشرح مفيد للمطور."""

        context = f"""
الأمر المنفذ: {command}
النتيجة: {output}
الأخطاء: {error}
"""

        return self._call_ollama(context, system_prompt)
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        الحصول على معلومات النظام
        
        Returns:
            معلومات شاملة عن النظام
        """
        try:
            info = {
                "os": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "cpu": {
                    "count": psutil.cpu_count(),
                    "usage": psutil.cpu_percent(interval=1)
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "used": psutil.virtual_memory().used,
                    "percentage": psutil.virtual_memory().percent
                },
                "disk": {
                    "total": psutil.disk_usage('/').total if self.os_type != "windows" else psutil.disk_usage('C:').total,
                    "used": psutil.disk_usage('/').used if self.os_type != "windows" else psutil.disk_usage('C:').used,
                    "free": psutil.disk_usage('/').free if self.os_type != "windows" else psutil.disk_usage('C:').free
                },
                "processes": len(psutil.pids()),
                "boot_time": psutil.boot_time()
            }
            
            return info
            
        except Exception as e:
            return {"error": str(e)}
    
    def monitor_processes(self, pattern: str = None) -> List[Dict[str, Any]]:
        """
        مراقبة العمليات
        
        Args:
            pattern: نمط البحث في أسماء العمليات
            
        Returns:
            قائمة العمليات
        """
        processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    if pattern and pattern.lower() not in proc_info['name'].lower():
                        continue
                    
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "cpu_percent": proc_info['cpu_percent'],
                        "memory_percent": proc_info['memory_percent']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            return [{"error": str(e)}]
        
        # ترتيب حسب استخدام CPU
        processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
        return processes[:20]  # أعلى 20 عملية
    
    def generate_system_report(self) -> Dict[str, Any]:
        """
        إنشاء تقرير شامل عن النظام
        
        Returns:
            تقرير تفصيلي
        """
        system_info = self.get_system_info()
        top_processes = self.monitor_processes()
        
        # تحليل الحالة باستخدام AI
        system_prompt = """أنت محلل أنظمة خبير. بناءً على معلومات النظام المقدمة،
قدم تقرير شامل يتضمن:
1. حالة النظام العامة
2. استخدام الموارد
3. العمليات المهمة
4. توصيات للتحسين
5. تحذيرات إن وجدت

اجعل التقرير مفيد لمدير النظام."""

        context = f"""
معلومات النظام: {json.dumps(system_info, indent=2)}
أهم العمليات: {json.dumps(top_processes[:5], indent=2)}
"""

        analysis = self._call_ollama(context, system_prompt)
        
        return {
            "timestamp": time.time(),
            "system_info": system_info,
            "top_processes": top_processes,
            "analysis": analysis
        }

def main():
    """دالة الاختبار الرئيسية"""
    print("💻 Terminal Agent - AI Development Assistant")
    print("=" * 50)
    
    # تهيئة الوكيل
    agent = TerminalAgent()
    
    # تحليل أمر
    print("\n🔍 تحليل أمر:")
    analysis = agent.analyze_command("ls -la")
    print(f"الأمر: {analysis['command']}")
    print(f"خطير: {analysis['is_dangerous']}")
    print(f"التحليل: {analysis['analysis'][:200]}...")
    
    # اقتراح أمر
    print("\n💡 اقتراح أمر:")
    suggestion = agent.suggest_command("عرض استخدام القرص الصلب")
    print(f"المهمة: {suggestion['task']}")
    print(f"الاقتراحات: {suggestion['suggestions'][:200]}...")
    
    # معلومات النظام
    print("\n📊 معلومات النظام:")
    info = agent.get_system_info()
    if "error" not in info:
        print(f"النظام: {info['os']['system']}")
        print(f"استخدام CPU: {info['cpu']['usage']}%")
        print(f"استخدام الذاكرة: {info['memory']['percentage']}%")

if __name__ == "__main__":
    main()
