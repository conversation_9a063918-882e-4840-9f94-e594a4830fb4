@echo off
chcp 65001 >nul
echo 🚀 تشغيل نظام الذكاء الاصطناعي السريع
echo =====================================

cd /d "C:\Users\<USER>\anything llm"

echo 📋 فحص حالة Docker...
docker --version
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت أو غير مشغل
    pause
    exit /b 1
)

echo 🐳 تشغيل الخدمات الأساسية...
echo تشغيل AI Coordinator...
docker start ai-coordinator

echo تشغيل Ollama...
docker start ollama

echo تشغيل AnythingLLM...
docker start anythingllm

echo تشغيل n8n...
docker start n8n

echo ⏳ انتظار تشغيل الخدمات...
timeout /t 10 /nobreak >nul

echo 📊 فحص حالة الخدمات...
docker ps --filter "name=ai-coordinator|ollama|anythingllm|n8n" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo ✅ النظام جاهز!
echo.
echo 🌐 الروابط المباشرة:
echo   • AI Coordinator: http://localhost:4003
echo   • Ollama: http://localhost:11434
echo   • AnythingLLM: http://localhost:4001
echo   • n8n: http://localhost:4002
echo.
echo 🎮 الواجهة السريعة:
start "" "file:///C:/Users/<USER>/anything llm/augment-assistants/quick-web-interface.html"

echo.
echo 💡 أوامر مفيدة:
echo   docker ps - عرض الحاويات النشطة
echo   docker logs [container] - عرض سجلات الحاوية
echo   docker stop [container] - إيقاف حاوية
echo.
pause
