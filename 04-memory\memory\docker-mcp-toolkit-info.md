# Docker Desktop MCP Toolkit - معلومات الإضافة
# ===============================================

## 📅 تاريخ التوثيق: 2025-07-05

## 🔍 نظرة عامة على MCP Toolkit:

### ما هو MCP Toolkit؟
- **الاسم الكامل**: Model Context Protocol Toolkit
- **المطور**: Docker Labs
- **الغرض**: إدارة وتشغيل خوادم MCP من خلال Docker Desktop
- **الإصدار**: متكامل مع Docker Desktop 4.42+

## 🖥️ الخوادم المتاحة في Docker Desktop:

### 1. **AWK Core MCP Server**
- **الوظيفة**: معالجة النصوص والبيانات باستخدام AWK
- **الاستخدام**: تحليل ملفات CSV، معالجة السجلات، تنسيق البيانات
- **المنفذ**: يتم تعيينه تلقائياً
- **الحالة**: مفعل ✅

### 2. **AWS Diagram MCP Server**
- **الوظيفة**: إنشاء مخططات البنية التحتية لـ AWS
- **الاستخدام**: رسم الشبكات، توثيق الخدمات، تصميم الحلول
- **المتطلبات**: AWS credentials (اختياري)
- **الحالة**: مفعل ✅

### 3. **Azure MCP Server**
- **الوظيفة**: التفاعل مع خدمات Microsoft Azure
- **الاستخدام**: إدارة الموارد، مراقبة الخدمات، النشر
- **المتطلبات**: Azure credentials
- **الحالة**: مفعل ✅

### 4. **Desktop Commander MCP Server**
- **الوظيفة**: تنفيذ أوامر النظام وإدارة الملفات
- **الاستخدام**: أتمتة المهام، إدارة الملفات، تشغيل السكريبتات
- **الأذونات**: يحتاج صلاحيات النظام
- **الحالة**: مفعل ✅

### 5. **Docker MCP Server**
- **الوظيفة**: إدارة حاويات وصور Docker
- **الاستخدام**: تشغيل الحاويات، مراقبة الأداء، إدارة الشبكات
- **المنفذ**: 8811 (مشغل حالياً)
- **الحالة**: مفعل ✅

### 6. **DuckDuckGo MCP Server**
- **الوظيفة**: البحث في الإنترنت باستخدام DuckDuckGo
- **الاستخدام**: البحث عن المعلومات، جمع البيانات، البحث الآمن
- **المزايا**: خصوصية عالية، بدون تتبع
- **الحالة**: مفعل ✅

### 7. **Filesystem (Reference) MCP Server**
- **الوظيفة**: إدارة نظام الملفات والمجلدات
- **الاستخدام**: قراءة/كتابة الملفات، تنظيم المجلدات، نسخ البيانات
- **الأمان**: وصول محدود للمجلدات المحددة
- **الحالة**: مفعل ✅

### 8. **GitHub Official MCP Server**
- **الوظيفة**: التفاعل مع GitHub API
- **الاستخدام**: إدارة المستودعات، إنشاء Issues، مراجعة PRs
- **المتطلبات**: GitHub Personal Access Token
- **الحالة**: مفعل ✅

### 9. **OpenAPI Schema MCP Server**
- **الوظيفة**: العمل مع مخططات OpenAPI/Swagger
- **الاستخدام**: توثيق APIs، اختبار الخدمات، توليد الكود
- **التنسيقات**: JSON, YAML
- **الحالة**: مفعل ✅

## 🎮 كيفية استخدام الإضافة:

### من Docker Desktop:
1. **فتح MCP Toolkit**: Extensions → MCP Toolkit
2. **تشغيل الخوادم**: النقر على أزرار التبديل
3. **مراقبة الحالة**: عرض الخوادم المشغلة
4. **إدارة الإعدادات**: تكوين كل خادم حسب الحاجة

### من سطر الأوامر:
```bash
# عرض الحاويات المشغلة
docker ps --filter "label=com.docker.compose.project=mcp"

# فحص صحة خادم معين
curl http://localhost:8811/health

# عرض سجلات خادم
docker logs mcp-docker-server
```

## 🔗 التكامل مع AnythingLLM:

### خطوات الربط:
1. **تشغيل الخوادم**: من Docker Desktop MCP Toolkit
2. **فتح AnythingLLM**: `http://localhost:3001`
3. **إعدادات التكامل**: Settings → Integrations → MCP Servers
4. **إضافة الخوادم**: إدخال عناوين الخوادم المحلية

### عناوين الخوادم:
```
Docker MCP: http://localhost:8811
GitHub MCP: http://localhost:8801 (إذا تم تشغيله منفصلاً)
Filesystem MCP: http://localhost:8802
DuckDuckGo MCP: http://localhost:8803
```

## ⚙️ الإعدادات المطلوبة:

### متغيرات البيئة:
```bash
# GitHub
GITHUB_TOKEN=ghp_xxxxxxxxxxxx

# Azure
AZURE_CLIENT_ID=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
AZURE_CLIENT_SECRET=xxxxxxxxxxxx
AZURE_TENANT_ID=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

# AWS (اختياري)
AWS_ACCESS_KEY_ID=AKIAXXXXXXXXXXXXXXXX
AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION=us-east-1
```

## 🛠️ استكشاف الأخطاء:

### مشاكل شائعة:
1. **الخادم لا يعمل**: فحص Docker Desktop وإعادة تشغيل الخادم
2. **مشكلة الأذونات**: تشغيل Docker Desktop كمدير
3. **مشكلة الشبكة**: فحص إعدادات Firewall
4. **مشكلة التوكنات**: التأكد من صحة API tokens

### أوامر التشخيص:
```bash
# فحص حالة Docker
docker system info

# فحص الشبكات
docker network ls

# فحص استخدام الموارد
docker stats

# تنظيف النظام
docker system prune -f
```

## 📊 مراقبة الأداء:

### مؤشرات مهمة:
- **استخدام CPU**: مراقبة عبر `docker stats`
- **استخدام الذاكرة**: فحص Memory usage
- **حركة الشبكة**: مراقبة Network I/O
- **مساحة القرص**: فحص Disk usage

### أدوات المراقبة:
- Docker Desktop Dashboard
- Docker CLI commands
- Windows Task Manager
- Resource Monitor

## 🔒 الأمان والخصوصية:

### أفضل الممارسات:
1. **استخدام توكنات محدودة**: أقل الصلاحيات المطلوبة
2. **تشفير البيانات الحساسة**: عدم تخزين كلمات المرور في النص
3. **مراقبة الوصول**: تسجيل جميع العمليات
4. **تحديث دوري**: الحفاظ على أحدث الإصدارات

### إعدادات الأمان:
- تقييد الوصول للملفات الحساسة
- استخدام شبكات Docker معزولة
- تفعيل logging للعمليات المهمة
- نسخ احتياطي دوري للإعدادات

## 📝 ملاحظات مهمة:

### المزايا:
- ✅ سهولة الإدارة من واجهة واحدة
- ✅ تكامل مباشر مع Docker Desktop
- ✅ دعم متعدد الخوادم
- ✅ مراقبة الحالة في الوقت الفعلي

### القيود:
- ⚠️ يتطلب Docker Desktop 4.42+
- ⚠️ بعض الخوادم تحتاج إعدادات إضافية
- ⚠️ استهلاك موارد النظام
- ⚠️ يحتاج صلاحيات إدارية

## 🚀 التطوير المستقبلي:

### خطط التحسين:
- إضافة خوادم MCP جديدة
- تحسين واجهة الإدارة
- دعم إعدادات متقدمة
- تكامل أفضل مع أدوات التطوير

### الميزات المنتظرة:
- دعم Kubernetes
- إعدادات أمان متقدمة
- مراقبة أداء محسنة
- دعم cloud deployments
