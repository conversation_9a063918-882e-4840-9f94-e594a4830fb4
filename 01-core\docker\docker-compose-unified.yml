version: '3.8'

# AI Development Assistant - Unified Integration System
# ====================================================
# Unified ports for better integration and communication:
# - Frontend Gateway: 3000
# - AnythingLLM: 3001
# - n8n: 3002  
# - AI Coordinator: 3003
# - Ollama WebUI: 3004
# - Integration API: 3005
# - Ollama API: 11434 (unchanged)

networks:
  unified-ai-network:
    driver: bridge
    name: unified-ai-network

volumes:
  # AnythingLLM volumes
  anythingllm_storage:
    driver: local
  anythingllm_hotdir:
    driver: local
  anythingllm_outputs:
    driver: local
  
  # n8n volumes
  n8n_data:
    driver: local
    
  # Ollama WebUI volumes
  ollama_webui_data:
    driver: local
  
  # Integration API volumes
  integration_data:
    driver: local
  
  # Shared memory volumes
  shared_memory_data:
    driver: local

services:
  # ==========================================
  # Frontend Gateway - Entry Point
  # ==========================================
  frontend-gateway:
    image: nginx:alpine
    container_name: frontend-gateway
    restart: unless-stopped
    ports:
      - "3000:80"
    volumes:
      - ./gateway/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./gateway/html:/usr/share/nginx/html:ro
    depends_on:
      - anythingllm
      - n8n
      - ai-coordinator
      - integration-api
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # AnythingLLM - Knowledge Management
  # ==========================================
  anythingllm:
    container_name: anythingllm
    image: mintplexlabs/anythingllm:latest
    restart: unless-stopped
    cap_add:
      - SYS_ADMIN
    ports:
      - "3001:3001"
    volumes:
      - anythingllm_storage:/app/server/storage
      - anythingllm_hotdir:/app/collector/hotdir
      - anythingllm_outputs:/app/server/storage/exports
    environment:
      - STORAGE_DIR=/app/server/storage
      - UID=1000
      - GID=1000
      - LLM_PROVIDER=ollama
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - EMBEDDING_ENGINE=ollama
      - EMBEDDING_BASE_URL=http://host.docker.internal:11434
      - EMBEDDING_MODEL_PREF=nomic-embed-text:latest
      - VECTOR_DB=lancedb
      - WHISPER_PROVIDER=local
      - TTS_PROVIDER=native
      - PASSWORDMINCHAR=8
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # n8n - Workflow Automation
  # ==========================================
  n8n:
    container_name: n8n
    image: n8nio/n8n:latest
    restart: unless-stopped
    ports:
      - "3002:5678"
    environment:
      # Basic Authentication
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=password123
      
      # Database
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite
      
      # General
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - WEBHOOK_URL=http://localhost:3002/
      - GENERIC_TIMEZONE=UTC
      
      # Integration settings
      - N8N_METRICS=true
      - N8N_DIAGNOSTICS_ENABLED=false
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows:ro
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # AI Coordinator - Service Coordination
  # ==========================================
  ai-coordinator:
    container_name: ai-coordinator
    build: 
      context: ./ai-coordinator
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3003:3333"
    environment:
      # Server Configuration
      - NODE_ENV=production
      - PORT=3333
      - HOST=0.0.0.0
      
      # Service URLs (unified ports)
      - ANYTHINGLLM_URL=http://anythingllm:3001
      - N8N_URL=http://n8n:5678
      - OLLAMA_URL=http://host.docker.internal:11434
      - INTEGRATION_API_URL=http://integration-api:5000
      
      # API Keys
      - GEMINI_API_KEY=AIzaSyAdt3Y3CIkv9zIb0__rRoNazF6bepTm4UY
      - OPENAI_API_KEY=
      
      # Database
      - DATABASE_URL=sqlite:///app/data/coordinator.db
      
      # Redis for caching
      - REDIS_URL=redis://shared-memory:6379
    volumes:
      - ./ai-coordinator/data:/app/data
      - ./ai-coordinator/logs:/app/logs
    depends_on:
      - shared-memory
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Ollama WebUI - Local AI Interface
  # ==========================================
  ollama-webui:
    container_name: ollama-webui
    image: ghcr.io/open-webui/open-webui:main
    restart: unless-stopped
    ports:
      - "3004:8080"
    environment:
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - WEBUI_SECRET_KEY=unified-ai-secret-key-2025
      - WEBUI_AUTH=false
      - DEFAULT_MODELS=llama3:8b,gemma3n:e4b,codellama:7b,mistral:7b
    volumes:
      - ollama_webui_data:/app/backend/data
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Integration API - Service Communication
  # ==========================================
  integration-api:
    container_name: integration-api
    build:
      context: ./integration-api
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3005:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_APP=app.py
      - PORT=5000
      
      # Service URLs
      - ANYTHINGLLM_URL=http://anythingllm:3001
      - N8N_URL=http://n8n:5678
      - AI_COORDINATOR_URL=http://ai-coordinator:3333
      - OLLAMA_URL=http://host.docker.internal:11434
      - OLLAMA_WEBUI_URL=http://ollama-webui:8080
      
      # Shared memory
      - REDIS_URL=redis://shared-memory:6379
      
      # API Keys
      - GEMINI_API_KEY=AIzaSyAdt3Y3CIkv9zIb0__rRoNazF6bepTm4UY
    volumes:
      - integration_data:/app/data
      - ./integration-api/logs:/app/logs
    depends_on:
      - shared-memory
      - anythingllm
      - n8n
      - ai-coordinator
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Shared Memory - Redis for Communication
  # ==========================================
  shared-memory:
    container_name: shared-memory
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - shared_memory_data:/data
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # System Monitor - Health Monitoring
  # ==========================================
  system-monitor:
    container_name: system-monitor
    build:
      context: ./system-monitor
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3006:8080"
    environment:
      - MONITOR_INTERVAL=30
      - SERVICES_TO_MONITOR=anythingllm:3001,n8n:5678,ai-coordinator:3333,ollama-webui:8080,integration-api:5000
      - REDIS_URL=redis://shared-memory:6379
    volumes:
      - ./system-monitor/logs:/app/logs
    depends_on:
      - shared-memory
    networks:
      - unified-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 60s
      timeout: 15s
      retries: 3

# ==========================================
# Health Check for External Services
# ==========================================
# Note: Ollama runs on host at port 11434
# Make sure it's running: docker run -d -p 11434:11434 ollama/ollama
