#!/usr/bin/env python3
"""
سكريبت تثبيت المتطلبات للنظام الذكي المتكامل
AI System Dependencies Installer
"""

import subprocess
import sys
import os

def print_colored(message, color="green"):
    """طباعة رسالة ملونة"""
    colors = {
        "green": "\033[92m",
        "red": "\033[91m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, '')}{message}{colors['reset']}")

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_colored("❌ يتطلب Python 3.8 أو أحدث", "red")
        return False
    print_colored(f"✅ Python {version.major}.{version.minor}.{version.micro}", "green")
    return True

def install_package(package_name):
    """تثبيت حزمة واحدة"""
    try:
        print_colored(f"📦 تثبيت {package_name}...", "blue")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name, "--quiet"
        ])
        print_colored(f"✅ تم تثبيت {package_name}", "green")
        return True
    except subprocess.CalledProcessError:
        print_colored(f"❌ فشل تثبيت {package_name}", "red")
        return False

def install_from_requirements():
    """تثبيت من ملف requirements.txt"""
    if os.path.exists("requirements.txt"):
        try:
            print_colored("📋 تثبيت من requirements.txt...", "blue")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print_colored("✅ تم تثبيت جميع المتطلبات من requirements.txt", "green")
            return True
        except subprocess.CalledProcessError:
            print_colored("⚠️ فشل تثبيت بعض المتطلبات، محاولة تثبيت المكتبات الأساسية...", "yellow")
            return False
    else:
        print_colored("⚠️ ملف requirements.txt غير موجود", "yellow")
        return False

def install_basic_packages():
    """تثبيت المكتبات الأساسية"""
    basic_packages = [
        "requests",
        "python-dotenv",
        "colorama",
        "rich"
    ]
    
    success_count = 0
    for package in basic_packages:
        if install_package(package):
            success_count += 1
    
    return success_count == len(basic_packages)

def install_google_cloud_packages():
    """تثبيت مكتبات Google Cloud"""
    print_colored("\n☁️ تثبيت مكتبات Google Cloud (اختياري)...", "blue")
    
    gcp_packages = [
        "google-cloud-aiplatform",
        "google-cloud-firestore", 
        "google-cloud-storage",
        "vertexai"
    ]
    
    success_count = 0
    for package in gcp_packages:
        if install_package(package):
            success_count += 1
    
    if success_count > 0:
        print_colored(f"✅ تم تثبيت {success_count}/{len(gcp_packages)} من مكتبات Google Cloud", "green")
    else:
        print_colored("⚠️ لم يتم تثبيت مكتبات Google Cloud (يمكن تثبيتها لاحقاً)", "yellow")

def test_imports():
    """اختبار استيراد المكتبات"""
    print_colored("\n🧪 اختبار المكتبات المثبتة...", "blue")
    
    test_packages = [
        ("requests", "requests"),
        ("json", "json"),
        ("os", "os"),
        ("datetime", "datetime")
    ]
    
    success_count = 0
    for display_name, import_name in test_packages:
        try:
            __import__(import_name)
            print_colored(f"✅ {display_name}", "green")
            success_count += 1
        except ImportError:
            print_colored(f"❌ {display_name}", "red")
    
    return success_count == len(test_packages)

def create_test_script():
    """إنشاء سكريبت اختبار"""
    test_script = '''
#!/usr/bin/env python3
"""اختبار سريع للنظام"""

def test_system():
    try:
        import requests
        import json
        import os
        from datetime import datetime
        
        print("✅ جميع المكتبات الأساسية متاحة")
        print("🎉 النظام جاهز للاستخدام!")
        return True
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

if __name__ == "__main__":
    test_system()
'''
    
    with open("test_system.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print_colored("✅ تم إنشاء test_system.py", "green")

def main():
    """الدالة الرئيسية"""
    print_colored("🚀 مرحباً بك في مثبت النظام الذكي المتكامل", "blue")
    print_colored("=" * 50, "blue")
    
    # فحص Python
    if not check_python_version():
        sys.exit(1)
    
    # تحديث pip
    print_colored("\n🔧 تحديث pip...", "blue")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip", "--quiet"
        ])
        print_colored("✅ تم تحديث pip", "green")
    except:
        print_colored("⚠️ لم يتم تحديث pip", "yellow")
    
    # تثبيت المتطلبات
    print_colored("\n📦 تثبيت المتطلبات...", "blue")
    
    # محاولة تثبيت من requirements.txt أولاً
    if not install_from_requirements():
        # إذا فشل، تثبيت المكتبات الأساسية
        if not install_basic_packages():
            print_colored("❌ فشل تثبيت المكتبات الأساسية", "red")
            sys.exit(1)
    
    # تثبيت مكتبات Google Cloud (اختياري)
    try:
        install_google_cloud_packages()
    except:
        print_colored("⚠️ تخطي مكتبات Google Cloud", "yellow")
    
    # اختبار المكتبات
    if test_imports():
        print_colored("\n🎉 تم تثبيت جميع المتطلبات بنجاح!", "green")
    else:
        print_colored("\n⚠️ بعض المكتبات لم تُثبت بشكل صحيح", "yellow")
    
    # إنشاء سكريبت الاختبار
    create_test_script()
    
    print_colored("\n📋 الخطوات التالية:", "blue")
    print_colored("1. شغّل: python test_system.py", "green")
    print_colored("2. شغّل: python vscode_ai_controller.py --check-status", "green")
    print_colored("3. ابدأ النظام: docker-compose up -d", "green")

if __name__ == "__main__":
    main()
