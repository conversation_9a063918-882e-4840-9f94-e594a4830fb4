#!/usr/bin/env python3
"""
VS Code AI System Controller
مسؤول عن التحكم في النظام الذكي المتكامل من داخل VS Code
"""

import json
import os
import time
from typing import Dict, List
from datetime import datetime
import subprocess
import sys

# تثبيت المتطلبات تلقائياً
def install_requirements():
    """تثبيت المتطلبات تلقائياً"""
    try:
        import pkg_resources
        import requests
        from dotenv import load_dotenv
        print("✅ جميع المتطلبات مثبتة")
        return True
    except ImportError as e:
        print(f"🔧 تثبيت المتطلبات المفقودة...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
        except Exception as install_error:
            print(f"❌ خطأ في تثبيت المتطلبات: {install_error}")
            # تثبيت المكتبات الأساسية فقط
            basic_packages = ["requests", "python-dotenv"]
            for package in basic_packages:
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", package
                    ])
                except:
                    pass
            return False

# تثبيت المتطلبات عند الاستيراد
install_requirements()

try:
    import requests
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ بعض المكتبات غير متاحة، سيتم استخدام الوظائف الأساسية فقط")
    requests = None

class AISystemController:
    def __init__(self):
        # إعدادات الخدمات
        self.n8n_url = "http://localhost:5678"
        self.ollama_url = "http://localhost:11434"
        self.anything_llm_url = "http://localhost:3001"
        
        # إعدادات Google Cloud
        self.gcp_project_id = "ai-integrated-system-2025"
        self.gcp_region = "us-central1"
        
        # حدود التكلفة والاستخدام
        self.daily_cost_limit = 5.0
        self.daily_token_limit = 10000
        self.current_daily_cost = 0.0
        self.current_daily_tokens = 0
        
        # ملف التكوين
        self.config_file = ".ai_system_config.json"
        self.load_config()

    def load_config(self):
        """تحميل إعدادات النظام"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.current_daily_cost = config.get('daily_cost', 0.0)
                self.current_daily_tokens = config.get('daily_tokens', 0)
                self.last_reset = config.get('last_reset', datetime.now().strftime('%Y-%m-%d'))
        
        # إعادة تعيين العدادات اليومية إذا تغير اليوم
        today = datetime.now().strftime('%Y-%m-%d')
        if hasattr(self, 'last_reset') and self.last_reset != today:
            self.current_daily_cost = 0.0
            self.current_daily_tokens = 0
            self.last_reset = today
            self.save_config()

    def save_config(self):
        """حفظ إعدادات النظام"""
        config = {
            'daily_cost': self.current_daily_cost,
            'daily_tokens': self.current_daily_tokens,
            'last_reset': self.last_reset
        }
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

    def check_services_status(self) -> Dict[str, bool]:
        """فحص حالة جميع الخدمات"""
        services = {
            'n8n': self._check_service(self.n8n_url),
            'ollama': self._check_service(f"{self.ollama_url}/api/tags"),
            'anything_llm': self._check_service(f"{self.anything_llm_url}/api/v1/system/check"),
        }
        return services

    def _check_service(self, url: str) -> bool:
        """فحص خدمة واحدة"""
        try:
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_ollama_models(self) -> List[str]:
        """الحصول على قائمة النماذج المتاحة في Ollama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [model['name'] for model in models]
        except:
            pass
        return []

    def determine_task_complexity(self, prompt: str) -> str:
        """تحديد مستوى تعقيد المهمة"""
        complex_keywords = [
            'ابحث', 'تحليل عميق', 'استراتيجية', 'مشروع كامل', 
            'قاعدة بيانات', 'تطبيق', 'نظام', 'خطة تنفيذ',
            'research', 'analyze', 'strategy', 'database', 'application'
        ]
        
        simple_keywords = [
            'دالة', 'كود بسيط', 'اشرح', 'ترجم', 'لخص',
            'function', 'simple', 'explain', 'translate', 'summarize'
        ]
        
        prompt_lower = prompt.lower()
        
        if any(keyword in prompt_lower for keyword in complex_keywords):
            return 'complex'
        elif any(keyword in prompt_lower for keyword in simple_keywords):
            return 'simple'
        else:
            return 'medium'

    def execute_simple_task(self, prompt: str, model: str = 'llama3') -> Dict:
        """تنفيذ مهمة بسيطة باستخدام Ollama"""
        if not self._check_limits():
            return {'error': 'تم تجاوز الحدود اليومية'}
        
        try:
            payload = {
                'model': model,
                'prompt': prompt,
                'stream': False
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                # تقدير عدد الرموز
                estimated_tokens = len(prompt.split()) + len(result.get('response', '').split())
                self.current_daily_tokens += estimated_tokens
                self.save_config()
                
                return {
                    'success': True,
                    'response': result.get('response', ''),
                    'model_used': model,
                    'tokens_estimated': estimated_tokens,
                    'type': 'local'
                }
        except Exception as e:
            return {'error': f'خطأ في Ollama: {str(e)}'}

    def execute_complex_task(self, prompt: str) -> Dict:
        """تنفيذ مهمة معقدة باستخدام n8n workflow"""
        if not self._check_limits():
            return {'error': 'تم تجاوز الحدود اليومية'}
        
        try:
            # إرسال الطلب إلى n8n webhook
            webhook_url = f"{self.n8n_url}/webhook/ai-complex-task"
            payload = {
                'prompt': prompt,
                'timestamp': datetime.now().isoformat(),
                'source': 'vscode'
            }
            
            response = requests.post(webhook_url, json=payload, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                # تحديث العدادات
                cost = result.get('cost_estimate', 0.1)
                tokens = result.get('tokens_used', 500)
                
                self.current_daily_cost += cost
                self.current_daily_tokens += tokens
                self.save_config()
                
                return {
                    'success': True,
                    'response': result.get('final_response', ''),
                    'workflow_id': result.get('workflow_id'),
                    'models_used': result.get('models_used', []),
                    'cost_estimate': cost,
                    'tokens_used': tokens,
                    'type': 'complex'
                }
        except Exception as e:
            return {'error': f'خطأ في n8n: {str(e)}'}

    def _check_limits(self) -> bool:
        """فحص الحدود اليومية"""
        if self.current_daily_cost >= self.daily_cost_limit:
            print(f"⚠️  تحذير: تم تجاوز الحد اليومي للتكلفة (${self.daily_cost_limit})")
            return False
        
        if self.current_daily_tokens >= self.daily_token_limit:
            print(f"⚠️  تحذير: تم تجاوز الحد اليومي للرموز ({self.daily_token_limit})")
            return False
        
        return True

    def create_project_files(self, files_data: List[Dict], project_name: str = None):
        """إنشاء ملفات المشروع في VS Code"""
        if not project_name:
            project_name = f"ai_project_{int(time.time())}"
        
        project_dir = os.path.join(os.getcwd(), project_name)
        os.makedirs(project_dir, exist_ok=True)
        
        created_files = []
        for file_data in files_data:
            file_path = os.path.join(project_dir, file_data['name'])
            
            # إنشاء المجلدات الفرعية إذا لزم الأمر
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_data['content'])
            
            created_files.append(file_path)
            print(f"✅ تم إنشاء: {file_path}")
        
        return created_files

    def process_request(self, prompt: str, force_complexity: str = None) -> Dict:
        """معالجة طلب من المستخدم"""
        print(f"🤖 معالجة الطلب: {prompt[:100]}...")
        
        # تحديد مستوى التعقيد
        complexity = force_complexity or self.determine_task_complexity(prompt)
        print(f"📊 مستوى التعقيد: {complexity}")
        
        # فحص حالة الخدمات
        services = self.check_services_status()
        print(f"🔍 حالة الخدمات: {services}")
        
        # تنفيذ المهمة حسب التعقيد
        if complexity == 'simple':
            available_models = self.get_ollama_models()
            if 'llama3:8b' in available_models:
                result = self.execute_simple_task(prompt, 'llama3:8b')
            elif available_models:
                result = self.execute_simple_task(prompt, available_models[0])
            else:
                return {'error': 'لا توجد نماذج متاحة في Ollama'}
        
        elif complexity in ['medium', 'complex']:
            if services.get('n8n'):
                result = self.execute_complex_task(prompt)
            else:
                print("⚠️  n8n غير متاح، التبديل إلى النموذج المحلي...")
                result = self.execute_simple_task(prompt, 'llama3:8b')
        
        # طباعة الإحصائيات
        self.print_daily_stats()
        
        return result

    def print_daily_stats(self):
        """طباعة إحصائيات الاستخدام اليومي"""
        print(f"\n📈 إحصائيات اليوم:")
        print(f"💰 التكلفة: ${self.current_daily_cost:.3f} / ${self.daily_cost_limit}")
        print(f"🔤 الرموز: {self.current_daily_tokens} / {self.daily_token_limit}")
        remaining_cost = self.daily_cost_limit - self.current_daily_cost
        remaining_tokens = self.daily_token_limit - self.current_daily_tokens
        print(f"🟢 المتبقي: ${remaining_cost:.3f} | {remaining_tokens} رمز")

def main():
    """الدالة الرئيسية للتشغيل من سطر الأوامر"""
    controller = AISystemController()
    
    if len(sys.argv) < 2:
        print("الاستخدام: python vscode_ai_controller.py 'your prompt here'")
        print("أو: python vscode_ai_controller.py --check-status")
        return
    
    if sys.argv[1] == '--check-status':
        services = controller.check_services_status()
        models = controller.get_ollama_models()
        
        print("🔍 حالة النظام:")
        for service, status in services.items():
            status_icon = "✅" if status else "❌"
            print(f"{status_icon} {service}: {'متصل' if status else 'غير متصل'}")
        
        print(f"\n🤖 النماذج المتاحة في Ollama: {models}")
        controller.print_daily_stats()
        return
    
    # معالجة الطلب
    prompt = ' '.join(sys.argv[1:])
    result = controller.process_request(prompt)
    
    if 'error' in result:
        print(f"❌ خطأ: {result['error']}")
    else:
        print(f"\n📝 النتيجة:")
        print(result.get('response', 'لا توجد استجابة'))
        
        # إنشاء ملفات إذا كانت النتيجة تحتوي على كود
        if 'files' in result:
            files_created = controller.create_project_files(result['files'])
            print(f"\n📁 تم إنشاء {len(files_created)} ملف")

if __name__ == "__main__":
    main()
