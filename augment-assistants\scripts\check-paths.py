#!/usr/bin/env python3
"""
فحص المسارات - Path Checker
التأكد من صحة جميع المسارات في النظام
"""

import os
import sys
from pathlib import Path

class PathChecker:
    """فاحص المسارات"""
    
    def __init__(self):
        self.base_path = Path("C:/Users/<USER>/anything llm")
        self.assistants_path = self.base_path / "augment-assistants"
        
    def check_base_paths(self):
        """فحص المسارات الأساسية"""
        print("🔍 فحص المسارات الأساسية...")
        print("-" * 40)
        
        base_paths = {
            "مجلد العمل الرئيسي": "C:/Users/<USER>/anything llm",
            "مجلد المساعدين": "C:/Users/<USER>/anything llm/augment-assistants",
            "Gemini CLI": "C:/Users/<USER>/gemini-cli",
            "مجلد المستخدم": "C:/Users/<USER>"
        }
        
        results = {}
        for name, path in base_paths.items():
            exists = Path(path).exists()
            status = "✅" if exists else "❌"
            print(f"{status} {name}: {path}")
            results[name] = exists
        
        return results
    
    def check_assistants_structure(self):
        """فحص هيكل مجلد المساعدين"""
        print("\n🤖 فحص هيكل المساعدين...")
        print("-" * 40)
        
        if not self.assistants_path.exists():
            print("❌ مجلد المساعدين غير موجود!")
            return {}
        
        structure = {
            "README.md": self.assistants_path / "README.md",
            "PATHS.md": self.assistants_path / "PATHS.md",
            "quick-start.py": self.assistants_path / "quick-start.py",
            "shared-memory/": self.assistants_path / "shared-memory",
            "gemini-interface/": self.assistants_path / "gemini-interface",
            "agents-interface/": self.assistants_path / "agents-interface",
            "scripts/": self.assistants_path / "scripts",
            "logs/": self.assistants_path / "logs"
        }
        
        results = {}
        for name, path in structure.items():
            exists = path.exists()
            status = "✅" if exists else "❌"
            print(f"{status} {name}")
            results[name] = exists
        
        return results
    
    def check_memory_files(self):
        """فحص ملفات الذاكرة"""
        print("\n🧠 فحص ملفات الذاكرة...")
        print("-" * 40)
        
        memory_files = {
            "project-knowledge.json": self.assistants_path / "shared-memory" / "project-knowledge.json",
            "consultation-history.json": self.assistants_path / "gemini-interface" / "consultation-history.json"
        }
        
        results = {}
        for name, path in memory_files.items():
            exists = path.exists()
            status = "✅" if exists else "⚠️"
            print(f"{status} {name}")
            results[name] = exists
        
        return results
    
    def check_external_systems(self):
        """فحص الأنظمة الخارجية"""
        print("\n🌐 فحص الأنظمة الخارجية...")
        print("-" * 40)
        
        external_paths = {
            "AI Agents الأصليين": self.base_path / "ai-agents",
            "AnythingLLM": self.base_path / "anything-llm",
            "VS Code Settings": self.base_path / ".vscode",
            "Memory الكبير": self.base_path / "memory"
        }
        
        results = {}
        for name, path in external_paths.items():
            exists = path.exists()
            status = "✅" if exists else "⚠️"
            print(f"{status} {name}: {path}")
            results[name] = exists
        
        return results
    
    def test_path_access(self):
        """اختبار الوصول للمسارات"""
        print("\n🔐 اختبار الوصول للمسارات...")
        print("-" * 40)
        
        test_paths = [
            self.assistants_path,
            self.assistants_path / "shared-memory",
            self.assistants_path / "gemini-interface"
        ]
        
        results = {}
        for path in test_paths:
            try:
                # محاولة قراءة المجلد
                list(path.iterdir()) if path.is_dir() else path.read_text()
                print(f"✅ يمكن الوصول: {path.name}")
                results[str(path)] = True
            except PermissionError:
                print(f"❌ ممنوع الوصول: {path.name}")
                results[str(path)] = False
            except FileNotFoundError:
                print(f"⚠️ غير موجود: {path.name}")
                results[str(path)] = False
            except Exception as e:
                print(f"❌ خطأ في {path.name}: {str(e)[:50]}")
                results[str(path)] = False
        
        return results
    
    def get_current_working_directory(self):
        """الحصول على مجلد العمل الحالي"""
        print("\n📍 مجلد العمل الحالي...")
        print("-" * 40)
        
        cwd = Path.cwd()
        print(f"📁 المجلد الحالي: {cwd}")
        
        # التحقق من المسار النسبي للمساعدين
        relative_assistants = cwd / "augment-assistants"
        if relative_assistants.exists():
            print("✅ يمكن الوصول للمساعدين نسبياً")
        else:
            print("❌ لا يمكن الوصول للمساعدين نسبياً")
        
        return str(cwd)
    
    def generate_path_report(self):
        """إنشاء تقرير شامل للمسارات"""
        print("📋 تقرير فحص المسارات")
        print("=" * 50)
        
        # تشغيل جميع الفحوصات
        base_results = self.check_base_paths()
        structure_results = self.check_assistants_structure()
        memory_results = self.check_memory_files()
        external_results = self.check_external_systems()
        access_results = self.test_path_access()
        current_dir = self.get_current_working_directory()
        
        # حساب النتائج
        all_results = {**base_results, **structure_results, **memory_results, **external_results}
        total_checks = len(all_results)
        passed_checks = sum(all_results.values())
        
        print(f"\n📊 ملخص النتائج:")
        print(f"✅ نجح: {passed_checks}/{total_checks}")
        print(f"📈 النسبة: {(passed_checks/total_checks)*100:.1f}%")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        if passed_checks >= total_checks * 0.9:
            print("🎉 جميع المسارات تعمل بشكل ممتاز!")
        elif passed_checks >= total_checks * 0.7:
            print("✅ معظم المسارات تعمل، راجع العناصر المفقودة")
        else:
            print("⚠️ مشاكل كثيرة في المسارات، يحتاج إصلاح")
        
        # مسارات مهمة للنسخ
        print(f"\n📋 مسارات مهمة للنسخ:")
        print(f"المساعدين: {self.assistants_path}")
        print(f"Gemini CLI: C:/Users/<USER>")
        print(f"مجلد العمل: {current_dir}")
        
        return {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "success_rate": (passed_checks/total_checks)*100,
            "current_directory": current_dir,
            "assistants_path": str(self.assistants_path)
        }

def main():
    """الدالة الرئيسية"""
    checker = PathChecker()
    report = checker.generate_path_report()
    
    # حفظ التقرير
    report_file = Path("../logs/path-check-report.json")
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    import json
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير: {report_file}")
    
    return report["success_rate"] >= 70

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
